07-05 15:05:45.639 12620 12643 I TestRunner: started: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 15:05:45.643  1543  1736 I AiAiEcho: Predicting[0]:
07-05 15:05:45.644  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 15:05:45.649  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:05:45.650  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:05:45.662  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:05:45.665  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:05:45.666  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:05:45.670  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:05:45.672  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:05:45.674  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:05:45.677  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:05:45.679   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:05:45.681 12620 12643 D APP:SecurePrefsManager: saveSignInData: run
07-05 15:05:45.681   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:05:45.681 12620 12643 D APP:SecurePrefsManager: saveAccessToken: run
07-05 15:05:45.681 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:45.683   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:05:45.709 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:185)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:05:45.943 12620 12643 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:185)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:05:46.011 12620 12643 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:05:46.041 12620 12643 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 15:05:46.051 12620 12643 D APP:SecurePrefsManager: saveAccessToken: done
07-05 15:05:46.051 12620 12643 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 15:05:46.051 12620 12643 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 15:05:46.052 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:46.055 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:46.086 12620 12643 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 15:05:46.086 12620 12643 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 15:05:46.086 12620 12643 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 15:05:46.086 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:46.088 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:46.122 12620 12643 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 15:05:46.122 12620 12643 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 15:05:46.122 12620 12643 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 15:05:46.122 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:46.124 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:46.158 12620 12643 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 15:05:46.158 12620 12643 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 15:05:46.158 12620 12643 D APP:SecurePrefsManager: saveLoginTime: run
07-05 15:05:46.158 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:46.161 12620 12643 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:46.193 12620 12643 D APP:SecurePrefsManager: saveLoginTime: done
07-05 15:05:46.194 12620 12643 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 15:05:46.194 12620 12643 D APP:SecurePrefsManager: saveSignInData: done
07-05 15:05:46.386   336 12670 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:46.387 12620 12643 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 15:05:46.407   336 12672 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:46.427 12620 12643 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 15:05:46.461   796   930 D SplashScreenView: Build android.window.SplashScreenView{1dc965 V.E...... ......ID 0,0-0,0}
07-05 15:05:46.461   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 15:05:46.461   796   930 D SplashScreenView: Branding: view: android.view.View{929b63a G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 15:05:46.464  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 15:05:46.464   796   966 W Parcel  : Expecting binder but got null!
07-05 15:05:46.474 12620 12674 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 15:05:46.475 12620 12674 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 15:05:46.487 12620 12674 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 15:05:46.516   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:05:46.537   564  2245 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 15:05:46.537   564  2245 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 15:05:46.537   564  2245 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 15:05:46.537   564  2245 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 15:05:46.537   564  2245 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 15:05:46.537   564  2245 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 15:05:46.537   564  2245 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 15:05:46.537   564  2245 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 15:05:46.570  1166  1782 D EGL_emulation: app_time_stats: avg=2114.66ms min=234.81ms max=3994.52ms count=2
07-05 15:05:46.672 12620 12620 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 15:05:46.691 12620 12620 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 15:05:46.693 12620 12620 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 15:05:46.705   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4770
07-05 15:05:46.705   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4a70
07-05 15:05:46.728   796   966 D EGL_emulation: app_time_stats: avg=27555.78ms min=406.59ms max=81788.35ms count=3
07-05 15:05:46.736   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3810
07-05 15:05:46.737   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3810
07-05 15:05:46.815 12620 12620 D APP:MainActivity: onCreate: run
07-05 15:05:46.817 12620 12620 D APP:Constants: initializeData: run
07-05 15:05:46.817 12620 12620 D APP:Constants: initializeData: done
07-05 15:05:47.036   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2250
07-05 15:05:47.441 12620 12620 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10208; state: ENABLED
07-05 15:05:47.483 12620 12620 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 15:05:47.484 12620 12620 I APP:MainActivity: onCreate: done setting root view
07-05 15:05:47.511 12620 12620 I APP:MainActivity: onCreate: Done initializing drawer
07-05 15:05:47.566 12620 12620 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 15:05:48.050 12620 12620 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 223.780ms (1242.29 bytecodes/s) (8240B approximate peak alloc)
07-05 15:05:48.086 12620 12620 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 15:05:48.087 12620 12620 D APP:MainActivity: onCreate: Done
07-05 15:05:48.087 12620 12620 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 15:05:48.088 12620 12620 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:05:48.092 12620 12620 D APP:HomeFragment: onCreateView: run
07-05 15:05:48.178 12620 12620 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 15:05:48.179 12620 12620 I CodeRunManager: Feature HomeFragment.runThreadCheckAndCachePackagesRelated not found in config. Using default value: true
07-05 15:05:48.179 12620 12620 I APP:HomeFragment: silentSignIn: run
07-05 15:05:48.180 12620 12678 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: Cache directories created successfully
07-05 15:05:48.369 12620 12678 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 137.186ms (233.26 bytecodes/s) (2528B approximate peak alloc)
07-05 15:05:48.561 12620 12678 I APP:HomeFragment: sending get request to get_is_server_online_link: http://localhost:8000/__mock_get_is_server_online
07-05 15:05:48.591 12620 12620 D APP:Authenticator: silentSignIn: run
07-05 15:05:48.591 12620 12620 D APP:Authenticator: hasAccountStored: run
07-05 15:05:48.592 12620 12620 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:05:48.592 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:48.596 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:48.632   336 12679 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:48.632 12620 12678 D TrafficStats: tagSocket(103) with statsTag=0xffffffff, statsUid=-1
07-05 15:05:48.636 12620 12671 D TrafficStats: tagSocket(105) with statsTag=0xffffffff, statsUid=-1
07-05 15:05:48.650 12620 12620 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:05:48.650 12620 12620 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:05:48.650 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:48.655 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:48.677   336 12683 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:48.706 12620 12620 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:05:48.706 12620 12620 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:05:48.706 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:48.709 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:48.751 12620 12620 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:05:48.751 12620 12620 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 15:05:48.751 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:48.756 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:48.807 12620 12620 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 15:05:48.807 12620 12620 D APP:Authenticator: hasAccountStored: end: true
07-05 15:05:48.807 12620 12620 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:05:48.807 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:48.810 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:48.846 12620 12620 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:05:48.847 12620 12620 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:05:48.847 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:48.852 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:48.854 12620 12684 I APP:ScanAppCallable: run: pool-3-thread-1
07-05 15:05:48.858 12620 12684 I System.out: Directories created successfully
07-05 15:05:48.890 12620 12620 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:05:48.890 12620 12620 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:05:48.890 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:48.894 12620 12620 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:48.894 12620 12684 I APP:ScanAppCallable: converting com.android.camera2 icon drawable to bitmap
07-05 15:05:48.903 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:48.919 12620 12684 I APP:ScanAppCallable: converting com.android.chrome icon drawable to bitmap
07-05 15:05:48.928 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:48.930 12620 12620 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:05:48.943 12620 12684 I APP:ScanAppCallable: converting com.android.settings icon drawable to bitmap
07-05 15:05:48.954 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:48.968 12620 12684 I APP:ScanAppCallable: converting com.google.android.apps.docs icon drawable to bitmap
07-05 15:05:48.973 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:48.980 12620 12620 D APP:Authenticator: validateAccessToken: run
07-05 15:05:48.980 12620 12620 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 15:05:48.982 12620 12620 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:05:48.984 12620 12620 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:05:48.985 12620 12685 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:05:48.985 12620 12685 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:05:48.992 12620 12684 I APP:ScanAppCallable: converting com.google.android.apps.maps icon drawable to bitmap
07-05 15:05:49.002 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.019 12620 12684 I APP:ScanAppCallable: converting com.google.android.apps.messaging icon drawable to bitmap
07-05 15:05:49.030 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.033 12620 12685 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:05:49.034 12620 12620 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:05:49.035 12620 12620 D APP:Authenticator: validateAccessToken: end
07-05 15:05:49.037 12620 12620 D APP:Authenticator: silentSignIn: done
07-05 15:05:49.038 12620 12620 D APP:HomeFragment: silentSignIn: done
07-05 15:05:49.039 12620 12620 D APP:HomeFragment: onCreateView: done
07-05 15:05:49.041 12620 12687 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:05:49.042 12620 12687 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:49.047 12620 12684 I APP:ScanAppCallable: converting com.google.android.apps.photos icon drawable to bitmap
07-05 15:05:49.047 12620 12687 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:49.055 12620 12620 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 15:05:49.055 12620 12620 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:05:49.056 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.060 12620 12620 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 15:05:49.061 12620 12620 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:05:49.068 12620 12620 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10208; state: DISABLED
07-05 15:05:49.073 12620 12684 I APP:ScanAppCallable: converting com.google.android.apps.youtube.music icon drawable to bitmap
07-05 15:05:49.079 12620 12673 W Parcel  : Expecting binder but got null!
07-05 15:05:49.086 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.105 12620 12684 I APP:ScanAppCallable: converting com.google.android.calendar icon drawable to bitmap
07-05 15:05:49.115 12620 12687 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:05:49.120 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.123   336 12693 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:49.137 12620 12684 I APP:ScanAppCallable: converting com.google.android.contacts icon drawable to bitmap
07-05 15:05:49.143 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.144   398   428 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10208 pid=12620
07-05 15:05:49.144   398   428 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10208 => denied (443 us)
07-05 15:05:49.145   398   427 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10208 pid=0
07-05 15:05:49.145   398   427 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10208 => denied (868 us)
07-05 15:05:49.146   398   428 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10208 pid=12620
07-05 15:05:49.146   398   428 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10208 => denied (1069 us)
07-05 15:05:49.146   398   427 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10208 pid=0
07-05 15:05:49.146   398   427 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10208 => denied (953 us)
07-05 15:05:49.147   398   427 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10208 pid=0
07-05 15:05:49.147   398   428 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10208 pid=12620
07-05 15:05:49.148   398   427 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10208 => denied (741 us)
07-05 15:05:49.148   398   428 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10208 => denied (1577 us)
07-05 15:05:49.159 12620 12673 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:05:49.166 12620 12673 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 15:05:49.166 12620 12673 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 15:05:49.167 12620 12620 E RecyclerView: No adapter attached; skipping layout
07-05 15:05:49.177 12620 12684 I APP:ScanAppCallable: converting com.google.android.deskclock icon drawable to bitmap
07-05 15:05:49.185 12620 12673 D EGL_emulation: eglCreateContext: 0x720c5f5e1350: maj 3 min 1 rcv 4
07-05 15:05:49.192   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d62d0
07-05 15:05:49.192   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4410
07-05 15:05:49.206 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.238 12620 12684 I APP:ScanAppCallable: converting com.google.android.dialer icon drawable to bitmap
07-05 15:05:49.248   796   966 D EGL_emulation: app_time_stats: avg=49213.62ms min=49213.62ms max=49213.62ms count=1
07-05 15:05:49.257 12620 12673 D EGL_emulation: eglMakeCurrent: 0x720c5f5e1350: ver 3 1 (tinfo 0x720e77346080) (first time)
07-05 15:05:49.271 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.272   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 15:05:49.273 12620 12673 I Gralloc4: mapper 4.x is not supported
07-05 15:05:49.277   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 15:05:49.278   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 15:05:49.278 12620 12673 W Gralloc4: allocator 4.x is not supported
07-05 15:05:49.287 12620 12684 I APP:ScanAppCallable: converting com.google.android.gm icon drawable to bitmap
07-05 15:05:49.292 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.301 12620 12673 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:05:49.302 12620 12673 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:05:49.307 12620 12684 I APP:ScanAppCallable: converting com.google.android.youtube icon drawable to bitmap
07-05 15:05:49.317 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.337 12620 12684 I APP:ScanAppCallable: converting com.android.stk icon drawable to bitmap
07-05 15:05:49.342 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.358 12620 12684 I APP:ScanAppCallable: converting com.google.android.documentsui icon drawable to bitmap
07-05 15:05:49.370 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.384 12620 12684 I APP:ScanAppCallable: converting com.google.android.googlequicksearchbox icon drawable to bitmap
07-05 15:05:49.386 12620 12684 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:05:49.395   564   602 W ziparchive: Unable to open '/data/app/~~51Lb8hZTCsTcCLwq26F-Ag==/com.kewtoms.whatappsdo-qkB5xmGa2DJ0G2q3K6_c1w==/base.dm': No such file or directory
07-05 15:05:49.397   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +2s959ms
07-05 15:05:49.405 12620 12678 D APP:AppScraper: checkAppNeedScrape: disabled
07-05 15:05:49.405 12620 12678 D APP:AppScraper: scrapeWhenNeeded: disabled
07-05 15:05:49.405 12620 12678 D APP:AppScraper: sendScrapeData: disabled
07-05 15:05:49.405 12620 12678 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 15:05:49.410   564   593 W InputManager-JNI: Input channel object '790b101 Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 15:05:49.421  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 15:05:49.421  1166  1181 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef62ea60 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 15:05:49.434 12620 12620 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:05:49.434 12620 12620 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:05:49.434 12620 12620 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 15:05:49.434 12620 12620 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 15:05:49.435 12620 12620 I APP:Authenticator: silentSignIn: Validate success. Signing in..
07-05 15:05:49.435 12620 12620 D APP:Authenticator: signInUsingAccessToken:run
07-05 15:05:49.435 12620 12620 I APP:Authenticator: signInUsingAccessToken:: Sending login request:http://localhost:8000/__mock_sign_in_access_token
07-05 15:05:49.436 12620 12620 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:05:49.436 12620 12620 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:05:49.436 12620 12696 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:05:49.437 12620 12696 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:05:49.438 12620 12696 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:05:49.440   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4110
07-05 15:05:49.440   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4290
07-05 15:05:49.440 12620 12698 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:05:49.440   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4350
07-05 15:05:49.440   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d32d0
07-05 15:05:49.441 12620 12698 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:49.441   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0db0
07-05 15:05:49.441   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0db0
07-05 15:05:49.441 12620 12620 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:05:49.441 12620 12620 D APP:Authenticator: signInUsingAccessToken:end
07-05 15:05:49.441 12620 12620 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 15:05:49.443 12620 12698 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:49.471 12620 12620 D InsetsController: show(ime(), fromIme=false)
07-05 15:05:49.485  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.486  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.486  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null}, false)
07-05 15:05:49.488  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.489  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:05:49.489  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:05:49.491  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:05:49.492  1371  1371 I AndroidIME: AbstractIme.onActivate():86 PasswordIme.onActivate() : EditorInfo = inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:05:49.500 12620 12698 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:05:49.503  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:05:49.507   336 12704 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:49.511 12620 12698 E Volley  : [82] NetworkUtility.shouldRetryException: Unexpected response code 404 for http://localhost:8000/__mock_sign_in_access_token
07-05 15:05:49.517  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:05:49.518  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY
07-05 15:05:49.518  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:49.518  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:05:49.518  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:49.518  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:05:49.518  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:05:49.520  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:05:49.522  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.525  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.525  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.526  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:05:49.527  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:05:49.529  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:05:49.529  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:05:49.530  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:05:49.530  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:05:49.541  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:05:49.543  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:05:49.543   796   966 D EGL_emulation: app_time_stats: avg=2814.11ms min=2814.11ms max=2814.11ms count=1
07-05 15:05:49.544  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:05:49.544  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 PasswordIme.onDeactivate()
07-05 15:05:49.546 12620 12620 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10208; state: ENABLED
07-05 15:05:49.546  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 15:05:49.547  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:05:49.547  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:05:49.549  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 15:05:49.554  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:05:49.554  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 15:05:49.555  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:05:49.555 12620 12620 I AssistStructure: Flattened final assist data: 2432 bytes, containing 1 windows, 15 views
07-05 15:05:49.558  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:05:49.559 12620 12620 E APP:RequestUtils: getJsonObjectRequest.onErrorResponse: VolleyError: com.android.volley.ClientError
07-05 15:05:49.559 12620 12620 I APP:Authenticator: signInUsingAccessToken:: Login using access token failed
07-05 15:05:49.559  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:05:49.560  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:05:49.560  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:05:49.561  1371  1371 I AndroidIME: AbstractIme.onActivate():86 LatinIme.onActivate() : EditorInfo = inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:05:49.564  1371  1371 I Delight5Facilitator: Delight5Facilitator.initializeForIme():777 initializeForIme() : Locale = [en_US], layout = qwerty
07-05 15:05:49.568  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.cancelShutdown():55 cancelShutdown()
07-05 15:05:49.568  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.syncLanguagePacks():67 syncLanguagePacks()
07-05 15:05:49.571  1371 10352 I SpeechFactory: SpeechRecognitionFactory.maybeScheduleAutoPackDownloadForFallback():205 maybeScheduleAutoPackDownloadForFallback()
07-05 15:05:49.571  1371 10352 I FallbackOnDeviceRecognitionProvider: FallbackOnDeviceRecognitionProvider.maybeScheduleAutoPackDownload():195 maybeScheduleAutoPackDownload() for language tag en-US
07-05 15:05:49.572  1371  1371 I LatinIme: LatinIme.updateEnableInlineSuggestionsOnDecoderSideFlags():1003 inline flag updated to:false
07-05 15:05:49.573 12620 12620 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{7e7fa67 VFED..CL. .F...... 265,536-815,683 #7f0801b2 app:id/search_field aid=**********} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 15:05:49.583  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:05:49.583  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.585  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 15:05:49.587  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.596  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:05:49.596  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:05:49.606  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:05:49.615  1274  3332 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 15:05:49.615  1274  3332 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 15:05:49.619  1274  3332 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 15:05:49.632  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:1783064033
07-05 15:05:49.641  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:05:49.642  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.644  1274  3332 I FontLog : Pulling font file for id = 49, cache size = 7 [CONTEXT service_id=132 ]
07-05 15:05:49.646  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.647  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_OPTIONAL
07-05 15:05:49.648  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:49.649  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:05:49.650  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:49.651  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:05:49.651  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:05:49.652  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:05:49.654  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.655  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.655  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.656  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:05:49.656  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:05:49.658  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:05:49.659  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:05:49.661  1274  3332 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef66bdb0 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 15:05:49.662  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:05:49.663  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:05:49.669  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:05:49.669  1274  3332 I FontLog : Pulling font file for id = 49, cache size = 7 [CONTEXT service_id=132 ]
07-05 15:05:49.670  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.671  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.739 12620 12643 W FileTestStorage: Output properties is not supported.
07-05 15:05:49.742 12620 12643 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 15:05:49.745  1371  1939 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:05:49.788 12620 12643 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 15:05:49.788 12620 12643 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 15:05:49.788 12620 12643 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 15:05:49.788 12620 12643 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 15:05:49.807  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.811  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.812 12620 12620 D InsetsController: show(ime(), fromIme=true)
07-05 15:05:49.814 12620 12620 D InsetsController: show(ime(), fromIme=true)
07-05 15:05:49.821  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:49.823  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:05:49.829  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:05:49.835  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 15:05:49.839  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.840  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.847  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.870  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.871  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.879  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.897  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.897  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.911 12620 12643 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 15:05:49.913  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.925  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.925  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.936  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:05:49.952 12620 12620 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:05:49.953 12620 12620 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:05:49.954 12620 12620 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 15:05:49.973 12620 12620 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 15:05:49.973 12620 12620 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:05:49.973 12620 12620 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:05:49.998 12620 12620 I ViewInteraction: Performing 'type text(nonexistentapp)' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:05:50.008  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=8341005, downTime=8341005, deviceId=-1, source=0x1002, displayId=0, eventId=-861933331 }
07-05 15:05:50.048  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=8341047, downTime=8341005, deviceId=-1, source=0x1002, displayId=0, eventId=-588467022 }
07-05 15:05:50.051 12620 12620 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{7e7fa67 VFED..CL. .F.P..ID 265,126-815,273 #7f0801b2 app:id/search_field aid=**********} flags=0 reason=SHOW_SOFT_INPUT
07-05 15:05:50.052  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:05:50.058 12620 12620 D InsetsController: show(ime(), fromIme=true)
07-05 15:05:50.280 12620 12620 D UiControllerImpl: Injecting string: "nonexistentapp"
07-05 15:05:50.285   564  1879 D InputDispatcher: Touch mode switch rejected, caller (pid=0, uid=10208) doesn't own the focused window nor none of the previously interacted window
07-05 15:05:50.318  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.319  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.363  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.364  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.412  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.413  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.445  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.446  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.480 12620 12673 D EGL_emulation: app_time_stats: avg=75.83ms min=6.21ms max=285.01ms count=14
07-05 15:05:50.485  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.485  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.513  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.513  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.562  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.563  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.613  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.613  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.660  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.660  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.710  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.711  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.743  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.744  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.765  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.765  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.813  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:05:50.813  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:05:50.835 12620 12620 I ViewInteraction: Performing 'close keyboard' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:05:50.838 12620 12620 D IdlingRegistry: Registering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@206138e]
07-05 15:05:50.845 12620 12709 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 15:05:50.847  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:05:50.848  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:05:50.855  1371  1371 I InputBundle: InputBundle.consumeEvent():923 Skip consuming an event as keyboard status is 0
07-05 15:05:50.860  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:05:50.860  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.shutdown():77 shutdown()
07-05 15:05:50.871  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 LatinIme.onDeactivate()
07-05 15:05:50.875  1371  1872 E native  : E0000 00:00:1751727950.875746    1872 keyboard.cc:27] Cannot create a keyboard with 0 valid keys
07-05 15:05:50.880  1371  2057 I native  : I0000 00:00:1751727950.880025    2057 input-context-store.cc:255] Ignoring stale client request for FetchSuggestions
07-05 15:05:50.880  1371  2057 I native  : I0000 00:00:1751727950.880496    2057 input-context-store.cc:178] Ignoring stale client request for OverrideDecodedCandidates
07-05 15:05:50.888  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 100<101, inputStateId=0, lastInputStateId=85
07-05 15:05:50.891  1371  1939 D EGL_emulation: app_time_stats: avg=64.98ms min=4.08ms max=473.66ms count=16
07-05 15:05:50.972   796   966 D EGL_emulation: app_time_stats: avg=713.97ms min=533.40ms max=894.55ms count=2
07-05 15:05:51.103   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3150
07-05 15:05:51.192 12620 12620 D IdlingRegistry: Unregistering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@206138e]
07-05 15:05:51.196 12620 12620 I ViewInteraction: Performing 'single click' action on view view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>
07-05 15:05:51.200  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=8342197, downTime=8342197, deviceId=-1, source=0x1002, displayId=0, eventId=-968690962 }
07-05 15:05:51.241  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=8342239, downTime=8342197, deviceId=-1, source=0x1002, displayId=0, eventId=-886914962 }
07-05 15:05:51.257 12620 12620 I APP:HomeFragment: onCreateView: searchButton onClick
07-05 15:05:51.261 12620 12620 I APP:HomeFragment: onCreateView: userUsageCount: 0
07-05 15:05:51.264 12620 12620 I APP:AppSearcher: Searching for app: nonexistentapp
07-05 15:05:51.265 12620 12620 I APP:AppSearcher: jsonBody:{"data_str":"{\"allowSearch\":true,\"appIds\":[\"com.android.camera2\",\"com.android.chrome\",\"com.android.settings\",\"com.google.android.apps.docs\",\"com.google.android.apps.maps\",\"com.google.android.apps.messaging\",\"com.google.android.apps.photos\",\"com.google.android.apps.youtube.music\",\"com.google.android.calendar\",\"com.google.android.contacts\",\"com.google.android.deskclock\",\"com.google.android.dialer\",\"com.google.android.gm\",\"com.google.android.youtube\",\"com.android.stk\",\"com.google.android.documentsui\",\"com.google.android.googlequicksearchbox\"],\"query\":\"nonexistentapp\"}"}
07-05 15:05:51.266 12620 12620 I APP:AppSearcher: searchApp: Sending request to:http://localhost:8000/__mock_search_user_app
07-05 15:05:51.267 12620 12620 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:05:51.268 12620 12620 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:05:51.269   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(34)
07-05 15:05:51.270 12620 12711 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:05:51.272 12620 12711 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:05:51.279 12620 12711 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:05:51.280 12620 12620 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:05:51.282 12620 12715 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:05:51.284 12620 12715 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:05:51.285 12620 12620 I APP:HomeFragment: Hided soft keyboard
07-05 15:05:51.286 12620 12715 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:05:51.329 12620 12715 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:05:51.333   336 12719 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:51.334   336 12720 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10208 0}
07-05 15:05:51.340 12620 12682 I TEST_DEBUG: No results response: {"code":400,"is_success":false,"message":"Operation failed.","data":{},"error":"Operation failed."}
07-05 15:05:51.344 12620 12620 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:05:51.345 12620 12620 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:05:51.345 12620 12620 I APP:AppSearcher: onPostSuccess: responseJsonObj:{"code":400,"is_success":false,"message":"Operation failed.","data":{},"error":"Operation failed."}
07-05 15:05:51.345 12620 12620 D CompatibilityChangeReporter: Compat change id reported: 147798919; UID 10208; state: ENABLED
07-05 15:05:51.364   796   966 W Parcel  : Expecting binder but got null!
07-05 15:05:51.400   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:05:51.511 12620 12620 I ViewInteraction: Checking 'com.kewtoms.whatappsdo.model.AppSearcherTest$$ExternalSyntheticLambda0@ce47c3e' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 15:05:51.512 12620 12620 I TEST_DEBUG: Initial child count: 6
07-05 15:05:51.906 12620 12673 D EGL_emulation: app_time_stats: avg=82.63ms min=4.48ms max=485.97ms count=17
07-05 15:05:53.405 12620 12673 D EGL_emulation: app_time_stats: avg=499.59ms min=498.86ms max=500.66ms count=3
07-05 15:05:53.708   564  1879 W InputManager-JNI: Input channel object '2deb505 Toast (client)' was disposed without first being removed with the input manager!
07-05 15:05:53.719   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4110
07-05 15:05:53.720   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d17d0
07-05 15:05:53.720   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4950
07-05 15:05:53.720   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d17d0
07-05 15:05:54.515 12620 12620 I ViewInteraction: Checking 'com.kewtoms.whatappsdo.model.AppSearcherTest$$ExternalSyntheticLambda1@117fa9f' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 15:05:54.516 12620 12620 I TEST_DEBUG: Current child count after search: 6
07-05 15:05:54.543 12620 12620 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PAUSED
07-05 15:05:54.543 12620 12620 V ActivityScenario: Update currentActivityStage to PAUSED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:05:54.560 12620 12620 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69 in: PRE_ON_CREATE
07-05 15:05:54.561 12620 12620 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69
07-05 15:05:54.568 12620 12629 W toms.whatappsdo: Cleared Reference was only reachable from finalizer (only reported once)
07-05 15:05:54.573 12620 12620 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69 in: CREATED
07-05 15:05:54.574 12620 12620 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69
07-05 15:05:54.579 12620 12620 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69 in: STARTED
07-05 15:05:54.581 12620 12620 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69
07-05 15:05:54.587 12620 12620 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69 in: RESUMED
07-05 15:05:54.587 12620 12620 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69
07-05 15:05:54.591 12620 12673 W Parcel  : Expecting binder but got null!
07-05 15:05:54.607 12620 12631 W System  : A resource failed to call close.
07-05 15:05:54.608 12620 12631 W System  : A resource failed to call close.
07-05 15:05:54.608 12620 12631 W System  : A resource failed to call close.
07-05 15:05:54.646 12620 12673 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:05:54.694   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity: +167ms
07-05 15:05:54.725 12620 12673 D EGL_emulation: app_time_stats: avg=439.99ms min=320.57ms max=500.48ms count=3
07-05 15:05:54.733 12620 12620 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STOPPED
07-05 15:05:54.733 12620 12620 V ActivityScenario: Update currentActivityStage to STOPPED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:05:54.737 12620 12620 D AutofillManager: onActivityFinishing(): calling cancelLocked()
07-05 15:05:54.754 12620 12620 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: DESTROYED
07-05 15:05:54.754 12620 12620 V ActivityScenario: Update currentActivityStage to DESTROYED, currentActivity=null
07-05 15:05:54.760   564  1879 W InputManager-JNI: Input channel object '4af8b92 com.kewtoms.whatappsdo/com.kewtoms.whatappsdo.MainActivity (client)' was disposed without first being removed with the input manager!
07-05 15:05:54.771   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5010
07-05 15:05:54.771   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4ad0
07-05 15:05:54.771   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4bf0
07-05 15:05:54.771   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4ad0
07-05 15:05:54.805  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 15:05:54.806  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 15:05:54.807  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.kewtoms.whatappsdo fieldId=-1 fieldName=null extras=null}, false)
07-05 15:05:54.808  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 15:05:54.808   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3d50
07-05 15:05:54.808  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:05:54.813 12620 12620 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69 in: PAUSED
07-05 15:05:54.813 12620 12620 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@1eaae69
07-05 15:05:54.821  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 15:05:54.826 12620 12643 I TestRunner: finished: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
