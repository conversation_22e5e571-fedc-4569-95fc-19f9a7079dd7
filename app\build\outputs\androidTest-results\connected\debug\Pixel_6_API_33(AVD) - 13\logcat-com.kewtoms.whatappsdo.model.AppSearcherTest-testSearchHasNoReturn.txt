07-05 14:55:51.610 11536 11559 I TestRunner: started: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 14:55:51.610  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:55:51.610  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:55:51.611  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:55:51.615  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:55:51.619  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:55:51.621  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:55:51.625  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:55:51.628  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:55:51.629  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:55:51.633 11536 11559 E TestRunner: failed: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 14:55:51.634 11536 11559 E TestRunner: ----- begin exception -----
07-05 14:55:51.639 11536 11559 E TestRunner: java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties
07-05 14:55:51.639 11536 11559 E TestRunner: 	at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:55:51.639 11536 11559 E TestRunner: Caused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties
07-05 14:55:51.639 11536 11559 E TestRunner: 	at android.content.res.AssetManager.nativeOpenAsset(Native Method)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at android.content.res.AssetManager.open(AssetManager.java:904)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at android.content.res.AssetManager.open(AssetManager.java:881)
07-05 14:55:51.639 11536 11559 E TestRunner: 	at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)
07-05 14:55:51.639 11536 11559 E TestRunner: 	... 35 more
07-05 14:55:51.639 11536 11559 E TestRunner: ----- end exception -----
07-05 14:55:51.641   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:55:51.642   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:55:51.642   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:55:51.653 11536 11559 I TestRunner: finished: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
