07-05 15:25:37.190 13811 13833 I TestRunner: started: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 15:25:37.201  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:25:37.203  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:25:37.209  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:25:37.209   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:25:37.210   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:25:37.214  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:25:37.217  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:25:37.230   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:25:37.237 13811 13833 D APP:SecurePrefsManager: saveSignInData: run
07-05 15:25:37.238 13811 13833 D APP:SecurePrefsManager: saveAccessToken: run
07-05 15:25:37.238 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:37.264 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:214)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:25:37.538 13811 13833 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:214)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:25:37.624 13811 13833 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:25:37.662 13811 13833 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 15:25:37.678 13811 13833 D APP:SecurePrefsManager: saveAccessToken: done
07-05 15:25:37.678 13811 13833 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 15:25:37.678 13811 13833 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 15:25:37.678 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:37.682 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:37.716 13811 13833 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 15:25:37.716 13811 13833 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 15:25:37.716 13811 13833 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 15:25:37.716 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:37.719 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:37.757 13811 13833 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 15:25:37.757 13811 13833 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 15:25:37.757 13811 13833 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 15:25:37.758 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:37.759 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:37.794 13811 13833 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 15:25:37.794 13811 13833 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 15:25:37.794 13811 13833 D APP:SecurePrefsManager: saveLoginTime: run
07-05 15:25:37.794 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:37.797 13811 13833 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:37.830 13811 13833 D APP:SecurePrefsManager: saveLoginTime: done
07-05 15:25:37.830 13811 13833 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 15:25:37.831 13811 13833 D APP:SecurePrefsManager: saveSignInData: done
07-05 15:25:38.054   336 13859 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:38.055 13811 13833 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 15:25:38.080   336 13861 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:38.106 13811 13833 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 15:25:38.144   796   930 D SplashScreenView: Build android.window.SplashScreenView{955d697 V.E...... ......ID 0,0-0,0}
07-05 15:25:38.144   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 15:25:38.144   796   930 D SplashScreenView: Branding: view: android.view.View{b89084 G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 15:25:38.147   796   966 W Parcel  : Expecting binder but got null!
07-05 15:25:38.165  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 15:25:38.179 13811 13863 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 15:25:38.187 13811 13863 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 15:25:38.188   564  2526 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 15:25:38.188   564  2526 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 15:25:38.188   564  2526 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 15:25:38.188   564  2526 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 15:25:38.188   564  2526 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 15:25:38.188   564  2526 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 15:25:38.188   564  2526 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 15:25:38.188   564  2526 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 15:25:38.189 13811 13863 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 15:25:38.202   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:25:38.281  1166  1782 D EGL_emulation: app_time_stats: avg=2261.99ms min=250.57ms max=4273.40ms count=2
07-05 15:25:38.285   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d55b0
07-05 15:25:38.285   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3150
07-05 15:25:38.285   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3150
07-05 15:25:38.286   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d55b0
07-05 15:25:38.286   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3150
07-05 15:25:38.286   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3150
07-05 15:25:38.286   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d55b0
07-05 15:25:38.286   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d55b0
07-05 15:25:38.408   796   966 D EGL_emulation: app_time_stats: avg=438184.41ms min=438184.41ms max=438184.41ms count=1
07-05 15:25:38.435 13811 13811 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 15:25:38.436   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d07b0
07-05 15:25:38.447 13811 13811 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 15:25:38.447 13811 13811 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 15:25:38.579 13811 13811 D APP:MainActivity: onCreate: run
07-05 15:25:38.580 13811 13811 D APP:Constants: initializeData: run
07-05 15:25:38.580 13811 13811 D APP:Constants: initializeData: done
07-05 15:25:38.702   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d55b0
07-05 15:25:39.226 13811 13811 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10214; state: ENABLED
07-05 15:25:39.267 13811 13811 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 15:25:39.267 13811 13811 I APP:MainActivity: onCreate: done setting root view
07-05 15:25:39.295 13811 13811 I APP:MainActivity: onCreate: Done initializing drawer
07-05 15:25:39.358 13811 13811 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 15:25:39.922 13811 13811 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 266.091ms (1044.75 bytecodes/s) (8240B approximate peak alloc)
07-05 15:25:39.961 13811 13811 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 15:25:39.961 13811 13811 D APP:MainActivity: onCreate: Done
07-05 15:25:39.962 13811 13811 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 15:25:39.962 13811 13811 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:25:39.968 13811 13811 D APP:HomeFragment: onCreateView: run
07-05 15:25:40.071 13811 13811 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 15:25:40.072 13811 13811 I CodeRunManager: Feature HomeFragment.runThreadCheckAndCachePackagesRelated not found in config. Using default value: true
07-05 15:25:40.072 13811 13811 I APP:HomeFragment: silentSignIn: run
07-05 15:25:40.073 13811 13866 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: Cache directories created successfully
07-05 15:25:40.290 13811 13866 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 159.090ms (201.14 bytecodes/s) (2528B approximate peak alloc)
07-05 15:25:40.502 13811 13866 I APP:HomeFragment: sending get request to get_is_server_online_link: http://localhost:8000/__mock_get_is_server_online
07-05 15:25:40.536 13811 13811 D APP:Authenticator: silentSignIn: run
07-05 15:25:40.536 13811 13811 D APP:Authenticator: hasAccountStored: run
07-05 15:25:40.536 13811 13811 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:25:40.537 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:40.542 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:40.583   336 13867 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:40.585 13811 13866 D TrafficStats: tagSocket(103) with statsTag=0xffffffff, statsUid=-1
07-05 15:25:40.590 13811 13860 D TrafficStats: tagSocket(105) with statsTag=0xffffffff, statsUid=-1
07-05 15:25:40.611 13811 13811 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:25:40.611 13811 13811 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:25:40.611 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:40.614 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:40.641   336 13871 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:40.670 13811 13811 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:25:40.670 13811 13811 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:25:40.670 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:40.673 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:40.722 13811 13811 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:25:40.722 13811 13811 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 15:25:40.722 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:40.725 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:40.787 13811 13811 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 15:25:40.788 13811 13811 D APP:Authenticator: hasAccountStored: end: true
07-05 15:25:40.789 13811 13811 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:25:40.789 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:40.793 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:40.841 13811 13811 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:25:40.842 13811 13811 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:25:40.842 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:40.845 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:40.851 13811 13872 I APP:ScanAppCallable: run: pool-3-thread-1
07-05 15:25:40.854 13811 13872 I System.out: Directories created successfully
07-05 15:25:40.891 13811 13811 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:25:40.891 13811 13811 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:25:40.892 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:40.895 13811 13811 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:40.901 13811 13872 I APP:ScanAppCallable: converting com.android.camera2 icon drawable to bitmap
07-05 15:25:40.911 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:40.927 13811 13872 I APP:ScanAppCallable: converting com.android.chrome icon drawable to bitmap
07-05 15:25:40.937 13811 13811 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:25:40.938 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:40.952 13811 13872 I APP:ScanAppCallable: converting com.android.settings icon drawable to bitmap
07-05 15:25:40.960 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:40.975 13811 13872 I APP:ScanAppCallable: converting com.google.android.apps.docs icon drawable to bitmap
07-05 15:25:40.980 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:40.997 13811 13872 I APP:ScanAppCallable: converting com.google.android.apps.maps icon drawable to bitmap
07-05 15:25:41.002 13811 13811 D APP:Authenticator: validateAccessToken: run
07-05 15:25:41.005 13811 13811 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 15:25:41.005 13811 13811 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:25:41.007 13811 13811 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:25:41.009 13811 13873 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:25:41.009 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.010 13811 13873 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:25:41.024 13811 13872 I APP:ScanAppCallable: converting com.google.android.apps.messaging icon drawable to bitmap
07-05 15:25:41.030 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.045 13811 13872 I APP:ScanAppCallable: converting com.google.android.apps.photos icon drawable to bitmap
07-05 15:25:41.053 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.069 13811 13873 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:25:41.070 13811 13811 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:25:41.070 13811 13811 D APP:Authenticator: validateAccessToken: end
07-05 15:25:41.070 13811 13811 D APP:Authenticator: silentSignIn: done
07-05 15:25:41.071 13811 13811 D APP:HomeFragment: silentSignIn: done
07-05 15:25:41.073 13811 13875 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:25:41.077 13811 13875 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:41.078 13811 13811 D APP:HomeFragment: onCreateView: done
07-05 15:25:41.080 13811 13872 I APP:ScanAppCallable: converting com.google.android.apps.youtube.music icon drawable to bitmap
07-05 15:25:41.082 13811 13875 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:41.094 13811 13811 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 15:25:41.094 13811 13811 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:25:41.095 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.100 13811 13811 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 15:25:41.101 13811 13811 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:25:41.107 13811 13811 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10214; state: DISABLED
07-05 15:25:41.117 13811 13872 I APP:ScanAppCallable: converting com.google.android.calendar icon drawable to bitmap
07-05 15:25:41.119 13811 13862 W Parcel  : Expecting binder but got null!
07-05 15:25:41.130 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.156 13811 13875 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:25:41.163 13811 13872 I APP:ScanAppCallable: converting com.google.android.contacts icon drawable to bitmap
07-05 15:25:41.171   336 13881 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:41.181 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.197   398   778 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10214 pid=0
07-05 15:25:41.197   398   778 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10214 => denied (374 us)
07-05 15:25:41.197   398   545 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10214 pid=13811
07-05 15:25:41.197   398   545 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10214 => denied (314 us)
07-05 15:25:41.197   398   545 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10214 pid=13811
07-05 15:25:41.197   398   778 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10214 pid=0
07-05 15:25:41.198   398   778 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10214 => denied (666 us)
07-05 15:25:41.198   398   545 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10214 => denied (456 us)
07-05 15:25:41.198   398   545 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10214 pid=13811
07-05 15:25:41.198   398   778 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10214 pid=0
07-05 15:25:41.198   398   545 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10214 => denied (250 us)
07-05 15:25:41.198   398   778 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10214 => denied (250 us)
07-05 15:25:41.215 13811 13862 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:25:41.217 13811 13811 E RecyclerView: No adapter attached; skipping layout
07-05 15:25:41.220   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d46b0
07-05 15:25:41.220 13811 13872 I APP:ScanAppCallable: converting com.google.android.deskclock icon drawable to bitmap
07-05 15:25:41.221   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d58b0
07-05 15:25:41.222 13811 13862 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 15:25:41.223 13811 13862 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 15:25:41.242 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.244 13811 13862 D EGL_emulation: eglCreateContext: 0x720c5f5e47d0: maj 3 min 1 rcv 4
07-05 15:25:41.254   796   966 D EGL_emulation: app_time_stats: avg=16160.52ms min=16160.52ms max=16160.52ms count=1
07-05 15:25:41.262 13811 13872 I APP:ScanAppCallable: converting com.google.android.dialer icon drawable to bitmap
07-05 15:25:41.275 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.291 13811 13872 I APP:ScanAppCallable: converting com.google.android.gm icon drawable to bitmap
07-05 15:25:41.299 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.306 13811 13862 D EGL_emulation: eglMakeCurrent: 0x720c5f5e47d0: ver 3 1 (tinfo 0x720e77346080) (first time)
07-05 15:25:41.317 13811 13872 I APP:ScanAppCallable: converting com.google.android.youtube icon drawable to bitmap
07-05 15:25:41.348   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 15:25:41.348 13811 13862 I Gralloc4: mapper 4.x is not supported
07-05 15:25:41.351 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.352   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 15:25:41.352   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 15:25:41.353 13811 13862 W Gralloc4: allocator 4.x is not supported
07-05 15:25:41.365 13811 13872 I APP:ScanAppCallable: converting com.android.stk icon drawable to bitmap
07-05 15:25:41.370 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.379 13811 13862 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:25:41.380 13811 13862 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:25:41.388 13811 13872 I APP:ScanAppCallable: converting com.google.android.documentsui icon drawable to bitmap
07-05 15:25:41.403 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.416 13811 13872 I APP:ScanAppCallable: converting com.google.android.googlequicksearchbox icon drawable to bitmap
07-05 15:25:41.422 13811 13872 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:25:41.444 13811 13866 D APP:AppScraper: checkAppNeedScrape: disabled
07-05 15:25:41.444 13811 13866 D APP:AppScraper: scrapeWhenNeeded: disabled
07-05 15:25:41.445 13811 13866 D APP:AppScraper: sendScrapeData: disabled
07-05 15:25:41.446 13811 13866 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 15:25:41.482   564   602 W ziparchive: Unable to open '/data/app/~~hyomSSFHiOanEI67ZV8UlA==/com.kewtoms.whatappsdo-SPLP4se5kIL9512McM8V3A==/base.dm': No such file or directory
07-05 15:25:41.483   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +3s364ms
07-05 15:25:41.494   564  2526 W InputManager-JNI: Input channel object '7576f23 Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 15:25:41.512  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 15:25:41.513  1166  1735 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef633dd0 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 15:25:41.523 13811 13811 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:25:41.524 13811 13811 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:25:41.524 13811 13811 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 15:25:41.524 13811 13811 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 15:25:41.524 13811 13811 I APP:Authenticator: silentSignIn: Validate success. Signing in..
07-05 15:25:41.526 13811 13811 D APP:Authenticator: signInUsingAccessToken:run
07-05 15:25:41.526 13811 13811 I APP:Authenticator: signInUsingAccessToken:: Sending login request:http://localhost:8000/__mock_sign_in_access_token
07-05 15:25:41.528 13811 13811 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:25:41.529 13811 13811 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:25:41.532   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d23d0
07-05 15:25:41.532   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3210
07-05 15:25:41.532 13811 13884 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:25:41.532   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d50d0
07-05 15:25:41.533 13811 13884 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:25:41.538 13811 13884 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:25:41.538 13811 13811 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:25:41.538 13811 13811 D APP:Authenticator: signInUsingAccessToken:end
07-05 15:25:41.539 13811 13811 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 15:25:41.549 13811 13888 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:25:41.551 13811 13888 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:41.557 13811 13888 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:41.576 13811 13811 D InsetsController: show(ime(), fromIme=false)
07-05 15:25:41.590  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.590  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.591  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null}, false)
07-05 15:25:41.595  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:25:41.595  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:25:41.598  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.603  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:25:41.603  1371  1371 I AndroidIME: AbstractIme.onActivate():86 PasswordIme.onActivate() : EditorInfo = inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:25:41.613  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:25:41.622  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:25:41.623  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY
07-05 15:25:41.623  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:41.623  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:25:41.624  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:41.625  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:25:41.626  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:25:41.627 13811 13888 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:25:41.628   796   966 D EGL_emulation: app_time_stats: avg=3219.76ms min=3219.76ms max=3219.76ms count=1
07-05 15:25:41.628  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:25:41.630  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.633  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.633  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.634  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:25:41.634  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:25:41.634  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:25:41.634  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:25:41.637  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:25:41.637  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:25:41.641  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:25:41.645  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:25:41.645  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:25:41.645  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:25:41.645  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:25:41.646  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 PasswordIme.onDeactivate()
07-05 15:25:41.647   336 13892 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:41.647  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 15:25:41.649  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 15:25:41.652 13811 13888 E Volley  : [84] NetworkUtility.shouldRetryException: Unexpected response code 404 for http://localhost:8000/__mock_sign_in_access_token
07-05 15:25:41.657  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:25:41.657  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 15:25:41.661  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:25:41.664  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:25:41.666  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.667  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:25:41.670  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:25:41.674  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:25:41.674  1371  1371 I AndroidIME: AbstractIme.onActivate():86 LatinIme.onActivate() : EditorInfo = inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:25:41.677  1371  1371 I Delight5Facilitator: Delight5Facilitator.initializeForIme():777 initializeForIme() : Locale = [en_US], layout = qwerty
07-05 15:25:41.678  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.cancelShutdown():55 cancelShutdown()
07-05 15:25:41.678  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.syncLanguagePacks():67 syncLanguagePacks()
07-05 15:25:41.680  1371 10352 I SpeechFactory: SpeechRecognitionFactory.maybeScheduleAutoPackDownloadForFallback():205 maybeScheduleAutoPackDownloadForFallback()
07-05 15:25:41.682  1371 10352 I FallbackOnDeviceRecognitionProvider: FallbackOnDeviceRecognitionProvider.maybeScheduleAutoPackDownload():195 maybeScheduleAutoPackDownload() for language tag en-US
07-05 15:25:41.683  1371  1371 I LatinIme: LatinIme.updateEnableInlineSuggestionsOnDecoderSideFlags():1003 inline flag updated to:false
07-05 15:25:41.691  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:25:41.693 13811 13811 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10214; state: ENABLED
07-05 15:25:41.697 13811 13811 I AssistStructure: Flattened final assist data: 2432 bytes, containing 1 windows, 15 views
07-05 15:25:41.705  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.706 13811 13811 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1d1c0bd VFED..CL. .F...... 265,536-815,683 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 15:25:41.709 13811 13811 E APP:RequestUtils: getJsonObjectRequest.onErrorResponse: VolleyError: com.android.volley.ClientError
07-05 15:25:41.710 13811 13811 I APP:Authenticator: signInUsingAccessToken:: Login using access token failed
07-05 15:25:41.722  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 15:25:41.725  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:25:41.745  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:25:41.745  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.750  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.751  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_OPTIONAL
07-05 15:25:41.751  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:41.751  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:25:41.753  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:41.753  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:25:41.753  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:25:41.755  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:25:41.755  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:25:41.755  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:25:41.756  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.757  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.758  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.759  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:25:41.760  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:25:41.761  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:25:41.762  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:25:41.763  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:25:41.768  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:25:41.775  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:2125298645
07-05 15:25:41.775  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:25:41.779  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.779  1274  3332 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef611900 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 15:25:41.780  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.823  1274  3332 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 15:25:41.823  1274  3332 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 15:25:41.825  1274  3332 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 15:25:41.834  1274  3332 I FontLog : Pulling font file for id = 55, cache size = 6 [CONTEXT service_id=132 ]
07-05 15:25:41.840 13811 13833 W FileTestStorage: Output properties is not supported.
07-05 15:25:41.842  1274  3332 I FontLog : Pulling font file for id = 55, cache size = 6 [CONTEXT service_id=132 ]
07-05 15:25:41.842 13811 13833 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 15:25:41.848  1371  1939 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:25:41.897 13811 13833 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 15:25:41.898 13811 13833 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 15:25:41.898 13811 13833 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 15:25:41.898 13811 13833 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 15:25:41.914  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.917 13811 13811 D InsetsController: show(ime(), fromIme=true)
07-05 15:25:41.917  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.922  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:41.924  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:25:41.928  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:25:41.931  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 15:25:41.964  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:41.964  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:41.988  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:41.991  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:41.991  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.002 13811 13811 D InsetsController: show(ime(), fromIme=true)
07-05 15:25:42.004  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.017  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.017  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.025 13811 13833 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 15:25:42.025  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.047  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.049  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.060  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:25:42.078 13811 13811 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:25:42.080 13811 13811 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:25:42.082 13811 13811 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 15:25:42.106 13811 13811 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 15:25:42.106 13811 13811 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:25:42.107 13811 13811 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:25:42.131 13811 13811 I ViewInteraction: Performing 'type text(nonexistentapp)' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:25:42.144  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9533140, downTime=9533140, deviceId=-1, source=0x1002, displayId=0, eventId=-271604428 }
07-05 15:25:42.183  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9533182, downTime=9533140, deviceId=-1, source=0x1002, displayId=0, eventId=-908769637 }
07-05 15:25:42.185 13811 13811 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1d1c0bd VFED..CL. .F.P..ID 265,126-815,273 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
07-05 15:25:42.190  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:25:42.205 13811 13811 D InsetsController: show(ime(), fromIme=true)
07-05 15:25:42.431 13811 13811 D UiControllerImpl: Injecting string: "nonexistentapp"
07-05 15:25:42.438   564   698 D InputDispatcher: Touch mode switch rejected, caller (pid=0, uid=10214) doesn't own the focused window nor none of the previously interacted window
07-05 15:25:42.466  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.466  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.508  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.508  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.543  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.543  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.560 13811 13862 D EGL_emulation: app_time_stats: avg=88.67ms min=4.41ms max=272.31ms count=12
07-05 15:25:42.599  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.599  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.619 13811 13899 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 15:25:42.661  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.662  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.695  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.695  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.729  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.729  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.761  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.762  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.801  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.802  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.829  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.830  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.867  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.867  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.894  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.894  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.931  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:25:42.931  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:25:42.938 13811 13811 I ViewInteraction: Performing 'close keyboard' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:25:42.942 13811 13811 D IdlingRegistry: Registering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@45fc7af]
07-05 15:25:42.945  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:25:42.946  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:25:42.953  1371  1371 I InputBundle: InputBundle.consumeEvent():923 Skip consuming an event as keyboard status is 0
07-05 15:25:42.954  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:25:42.954  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.shutdown():77 shutdown()
07-05 15:25:42.955  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 LatinIme.onDeactivate()
07-05 15:25:42.962  1371  1872 E native  : E0000 00:00:1751729142.962667    1872 keyboard.cc:27] Cannot create a keyboard with 0 valid keys
07-05 15:25:42.974  1371  2057 I native  : I0000 00:00:1751729142.974941    2057 input-context-store.cc:255] Ignoring stale client request for FetchSuggestions
07-05 15:25:42.982  1371  1939 D EGL_emulation: app_time_stats: avg=64.00ms min=22.22ms max=495.64ms count=16
07-05 15:25:42.982  1371  2057 I native  : I0000 00:00:1751729142.982886    2057 input-context-store.cc:178] Ignoring stale client request for OverrideDecodedCandidates
07-05 15:25:42.985  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 162<163, inputStateId=0, lastInputStateId=145
07-05 15:25:43.024   796   966 D EGL_emulation: app_time_stats: avg=697.76ms min=422.82ms max=972.70ms count=2
07-05 15:25:43.185   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d57f0
07-05 15:25:43.303 13811 13811 D IdlingRegistry: Unregistering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@45fc7af]
07-05 15:25:43.307 13811 13811 I ViewInteraction: Performing 'single click' action on view view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>
07-05 15:25:43.311  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9534308, downTime=9534308, deviceId=-1, source=0x1002, displayId=0, eventId=-242978768 }
07-05 15:25:43.353  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9534351, downTime=9534308, deviceId=-1, source=0x1002, displayId=0, eventId=-855508256 }
07-05 15:25:43.374 13811 13811 I APP:HomeFragment: onCreateView: searchButton onClick
07-05 15:25:43.376 13811 13811 I APP:HomeFragment: onCreateView: userUsageCount: 0
07-05 15:25:43.380 13811 13811 I APP:AppSearcher: Searching for app: nonexistentapp
07-05 15:25:43.381 13811 13811 I APP:AppSearcher: jsonBody:{"data_str":"{\"allowSearch\":true,\"appIds\":[\"com.android.camera2\",\"com.android.chrome\",\"com.android.settings\",\"com.google.android.apps.docs\",\"com.google.android.apps.maps\",\"com.google.android.apps.messaging\",\"com.google.android.apps.photos\",\"com.google.android.apps.youtube.music\",\"com.google.android.calendar\",\"com.google.android.contacts\",\"com.google.android.deskclock\",\"com.google.android.dialer\",\"com.google.android.gm\",\"com.google.android.youtube\",\"com.android.stk\",\"com.google.android.documentsui\",\"com.google.android.googlequicksearchbox\"],\"query\":\"nonexistentapp\"}"}
07-05 15:25:43.382 13811 13811 I APP:AppSearcher: searchApp: Sending request to:http://localhost:8000/__mock_search_user_app
07-05 15:25:43.383 13811 13811 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:25:43.383 13811 13811 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:25:43.385 13811 13901 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:25:43.386 13811 13901 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:25:43.388   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(37)
07-05 15:25:43.390 13811 13901 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:25:43.392 13811 13811 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:25:43.394 13811 13811 I APP:HomeFragment: Hided soft keyboard
07-05 15:25:43.396 13811 13906 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:25:43.396 13811 13906 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:25:43.400 13811 13906 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:25:43.441 13811 13906 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:25:43.446   336 13909 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:43.448   336 13910 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10214 0}
07-05 15:25:43.449 13811 13870 I TEST_DEBUG: No results response: {"code":200,"is_success":true,"message":"Search completed with no results","Result":{"packageNames":[],"scores":[]}}
07-05 15:25:43.456 13811 13811 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:25:43.456 13811 13811 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:25:43.457 13811 13811 I APP:AppSearcher: onPostSuccess: responseJsonObj:{"code":200,"is_success":true,"message":"Search completed with no results","Result":{"packageNames":[],"scores":[]}}
07-05 15:25:44.006 13811 13862 D EGL_emulation: app_time_stats: avg=71.21ms min=4.44ms max=501.07ms count=20
07-05 15:25:45.022 13811 13862 D EGL_emulation: app_time_stats: avg=508.15ms min=499.58ms max=516.72ms count=2
07-05 15:25:46.522 13811 13862 D EGL_emulation: app_time_stats: avg=499.85ms min=498.23ms max=500.86ms count=3
07-05 15:25:46.719 13811 13811 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)}' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 15:25:46.756 13811 13811 D takeScreenshot: Found 1 global views to redraw
07-05 15:25:46.776 13811 13820 W toms.whatappsdo: Cleared Reference was only reachable from finalizer (only reported once)
07-05 15:25:46.812 13811 13822 W System  : A resource failed to call close.
07-05 15:25:46.812 13811 13822 W System  : A resource failed to call close.
07-05 15:25:46.812 13811 13822 W System  : A resource failed to call close.
07-05 15:25:46.837   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 15:25:46.837 13796 13810 I Gralloc4: mapper 4.x is not supported
07-05 15:25:46.847 13796 13810 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:25:46.855 13796 13914 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 15:25:46.855 13796 13914 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 15:25:46.858 13796 13914 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 15:25:46.878 13796 13914 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:25:46.879 13796 13914 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 15:25:46.880 13796 13914 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 15:25:46.901 13796 13914 D EGL_emulation: eglCreateContext: 0x73c2848a0f90: maj 3 min 1 rcv 4
07-05 15:25:46.930 13796 13914 D EGL_emulation: eglMakeCurrent: 0x73c2848a0f90: ver 3 1 (tinfo 0x73c4ab01b080) (first time)
07-05 15:25:46.989  1525  5177 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 15:25:47.048  1274 13636 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService started execution. cause:9 exec_start_elapsed_seconds: 9538 [CONTEXT service_id=218 ]
07-05 15:25:47.054  1274  1274 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 15:25:47.055  1274  1274 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 15:25:47.058  1274 13550 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService finished executing. cause:9 result: 1 elapsed_millis: 19 uptime_millis: 19 exec_start_elapsed_seconds: 9538 [CONTEXT service_id=218 ]
07-05 15:25:47.095 13811 13833 E TestRunner: failed: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 15:25:47.095 13811 13833 E TestRunner: ----- begin exception -----
07-05 15:25:47.101 13811 13833 E TestRunner: androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: '(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)' doesn't match the selected view.
07-05 15:25:47.101 13811 13833 E TestRunner: Expected: (view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)
07-05 15:25:47.101 13811 13833 E TestRunner:      Got: view.getGlobalVisibleRect() returned empty rectangle
07-05 15:25:47.101 13811 13833 E TestRunner: View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=0, height=0, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1878.0, child-count=0}
07-05 15:25:47.101 13811 13833 E TestRunner:
07-05 15:25:47.101 13811 13833 E TestRunner: 	at dalvik.system.VMStack.getThreadStackTrace(Native Method)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at java.lang.Thread.getStackTrace(Thread.java:1841)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:304)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:25:47.101 13811 13833 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:25:47.102 13811 13833 E TestRunner: Caused by: junit.framework.AssertionFailedError: '(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)' doesn't match the selected view.
07-05 15:25:47.102 13811 13833 E TestRunner: Expected: (view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)
07-05 15:25:47.102 13811 13833 E TestRunner:      Got: view.getGlobalVisibleRect() returned empty rectangle
07-05 15:25:47.102 13811 13833 E TestRunner: View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=0, height=0, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1878.0, child-count=0}
07-05 15:25:47.102 13811 13833 E TestRunner:
07-05 15:25:47.102 13811 13833 E TestRunner: 	at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at android.os.Looper.loop(Looper.java:288)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 15:25:47.102 13811 13833 E TestRunner: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 15:25:47.102 13811 13833 E TestRunner: ----- end exception -----
07-05 15:25:47.116 13811 13833 I TestRunner: finished: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
