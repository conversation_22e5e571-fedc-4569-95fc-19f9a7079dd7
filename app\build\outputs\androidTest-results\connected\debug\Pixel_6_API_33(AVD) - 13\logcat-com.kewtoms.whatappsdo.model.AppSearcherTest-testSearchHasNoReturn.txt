07-05 14:58:02.902 11802 11825 I TestRunner: started: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 14:58:02.912  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 14:58:02.914  1543  1736 I AiAiEcho: Predicting[0]:
07-05 14:58:02.914  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 14:58:02.929  1543  1736 I AiAiEcho: Predicting[0]:
07-05 14:58:02.930  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 14:58:02.934 11802 11825 D APP:SecurePrefsManager: saveSignInData: run
07-05 14:58:02.935 11802 11825 D APP:SecurePrefsManager: saveAccessToken: run
07-05 14:58:02.935 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:02.935  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:58:02.938  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:58:02.939  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:58:02.940  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:58:02.940  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:58:02.941  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:58:02.943  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:58:02.943   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:58:02.944   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:58:02.944  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:58:02.945  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:58:02.959   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:58:02.970 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:185)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:58:03.195 11802 11825 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:185)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:58:03.264 11802 11825 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:58:03.299 11802 11825 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 14:58:03.310 11802 11825 D APP:SecurePrefsManager: saveAccessToken: done
07-05 14:58:03.310 11802 11825 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 14:58:03.310 11802 11825 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 14:58:03.311 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:03.314 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:03.345 11802 11825 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 14:58:03.345 11802 11825 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 14:58:03.345 11802 11825 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 14:58:03.345 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:03.348 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:03.378 11802 11825 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 14:58:03.378 11802 11825 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 14:58:03.378 11802 11825 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 14:58:03.379 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:03.383 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:03.417 11802 11825 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 14:58:03.417 11802 11825 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 14:58:03.418 11802 11825 D APP:SecurePrefsManager: saveLoginTime: run
07-05 14:58:03.418 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:03.421 11802 11825 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:03.453 11802 11825 D APP:SecurePrefsManager: saveLoginTime: done
07-05 14:58:03.453 11802 11825 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 14:58:03.453 11802 11825 D APP:SecurePrefsManager: saveSignInData: done
07-05 14:58:03.654   336 11853 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:03.655 11802 11825 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 14:58:03.675   336 11855 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:03.695 11802 11825 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 14:58:03.730   796   930 D SplashScreenView: Build android.window.SplashScreenView{f57ae75 V.E...... ......ID 0,0-0,0}
07-05 14:58:03.730   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 14:58:03.730   796   930 D SplashScreenView: Branding: view: android.view.View{2fb1e0a G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 14:58:03.734   796   966 W Parcel  : Expecting binder but got null!
07-05 14:58:03.751  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 14:58:03.787   564  1879 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 14:58:03.787   564  1879 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 14:58:03.787   564  1879 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 14:58:03.787   564  1879 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 14:58:03.787   564  1879 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 14:58:03.787   564  1879 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 14:58:03.787   564  1879 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 14:58:03.787   564  1879 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 14:58:03.802 11802 11857 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 14:58:03.804 11802 11857 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 14:58:03.806 11802 11857 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 14:58:03.811   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:58:03.812  1166  1782 D EGL_emulation: app_time_stats: avg=1451.28ms min=53.91ms max=3937.74ms count=3
07-05 14:58:03.932 11802 11802 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 14:58:03.943 11802 11802 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 14:58:03.944 11802 11802 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 14:58:04.000   796   966 D EGL_emulation: app_time_stats: avg=190685.92ms min=401.71ms max=571111.31ms count=3
07-05 14:58:04.037 11802 11802 D APP:MainActivity: onCreate: run
07-05 14:58:04.038 11802 11802 D APP:Constants: initializeData: run
07-05 14:58:04.039 11802 11802 D APP:Constants: initializeData: done
07-05 14:58:04.053   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0ab0
07-05 14:58:04.336   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4bf0
07-05 14:58:04.557 11802 11802 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10204; state: ENABLED
07-05 14:58:04.605 11802 11802 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 14:58:04.605 11802 11802 I APP:MainActivity: onCreate: done setting root view
07-05 14:58:04.628 11802 11802 I APP:MainActivity: onCreate: Done initializing drawer
07-05 14:58:04.682 11802 11802 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 14:58:05.128 11802 11802 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 206.513ms (1346.16 bytecodes/s) (8240B approximate peak alloc)
07-05 14:58:05.164 11802 11802 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 14:58:05.164 11802 11802 D APP:MainActivity: onCreate: Done
07-05 14:58:05.165 11802 11802 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 14:58:05.166 11802 11802 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:58:05.172 11802 11802 D APP:HomeFragment: onCreateView: run
07-05 14:58:05.258 11802 11802 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 14:58:05.258 11802 11802 I CodeRunManager: Feature HomeFragment.runThreadCheckAndCachePackagesRelated not found in config. Using default value: true
07-05 14:58:05.259 11802 11802 I APP:HomeFragment: silentSignIn: run
07-05 14:58:05.260 11802 11860 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: Cache directories created successfully
07-05 14:58:05.449 11802 11860 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 135.362ms (236.40 bytecodes/s) (2528B approximate peak alloc)
07-05 14:58:05.679 11802 11860 I APP:HomeFragment: sending get request to get_is_server_online_link: http://localhost:8000/__mock_get_is_server_online
07-05 14:58:05.705 11802 11802 D APP:Authenticator: silentSignIn: run
07-05 14:58:05.706 11802 11802 D APP:Authenticator: hasAccountStored: run
07-05 14:58:05.706 11802 11802 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:58:05.707 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:05.711 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:05.753   336 11861 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:05.754 11802 11860 D TrafficStats: tagSocket(103) with statsTag=0xffffffff, statsUid=-1
07-05 14:58:05.758 11802 11854 D TrafficStats: tagSocket(105) with statsTag=0xffffffff, statsUid=-1
07-05 14:58:05.767 11802 11802 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:58:05.767 11802 11802 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:58:05.767 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:05.771 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:05.806   336 11865 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:05.818 11802 11802 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:58:05.819 11802 11802 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:58:05.819 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:05.822 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:05.870 11802 11802 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:58:05.870 11802 11802 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 14:58:05.871 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:05.873 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:05.921 11802 11802 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 14:58:05.921 11802 11802 D APP:Authenticator: hasAccountStored: end: true
07-05 14:58:05.921 11802 11802 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:58:05.921 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:05.924 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:05.966 11802 11802 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:58:05.966 11802 11802 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:58:05.966 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:05.968 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:05.976 11802 11866 I APP:ScanAppCallable: run: pool-3-thread-1
07-05 14:58:05.978 11802 11866 I System.out: Directories created successfully
07-05 14:58:06.013 11802 11802 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:58:06.013 11802 11802 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:58:06.014 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:06.016 11802 11802 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:06.022 11802 11866 I APP:ScanAppCallable: converting com.android.camera2 icon drawable to bitmap
07-05 14:58:06.031 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.047 11802 11866 I APP:ScanAppCallable: converting com.android.chrome icon drawable to bitmap
07-05 14:58:06.055 11802 11802 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:58:06.058 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.073 11802 11866 I APP:ScanAppCallable: converting com.android.settings icon drawable to bitmap
07-05 14:58:06.082 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.096 11802 11802 D APP:Authenticator: validateAccessToken: run
07-05 14:58:06.097 11802 11802 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 14:58:06.097 11802 11802 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:58:06.100 11802 11802 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:58:06.102 11802 11867 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:58:06.102 11802 11866 I APP:ScanAppCallable: converting com.google.android.apps.docs icon drawable to bitmap
07-05 14:58:06.102 11802 11867 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:58:06.115 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.133 11802 11866 I APP:ScanAppCallable: converting com.google.android.apps.maps icon drawable to bitmap
07-05 14:58:06.145 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.149 11802 11867 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:58:06.149 11802 11802 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:58:06.149 11802 11802 D APP:Authenticator: validateAccessToken: end
07-05 14:58:06.150 11802 11802 D APP:Authenticator: silentSignIn: done
07-05 14:58:06.151 11802 11802 D APP:HomeFragment: silentSignIn: done
07-05 14:58:06.151 11802 11802 D APP:HomeFragment: onCreateView: done
07-05 14:58:06.152 11802 11870 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:58:06.154 11802 11870 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:06.157 11802 11870 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:06.161 11802 11866 I APP:ScanAppCallable: converting com.google.android.apps.messaging icon drawable to bitmap
07-05 14:58:06.168 11802 11802 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 14:58:06.169 11802 11802 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:58:06.171 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.173 11802 11802 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 14:58:06.174 11802 11802 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:58:06.181 11802 11802 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10204; state: DISABLED
07-05 14:58:06.193 11802 11856 W Parcel  : Expecting binder but got null!
07-05 14:58:06.193 11802 11866 I APP:ScanAppCallable: converting com.google.android.apps.photos icon drawable to bitmap
07-05 14:58:06.199 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.214 11802 11870 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:58:06.217 11802 11866 I APP:ScanAppCallable: converting com.google.android.apps.youtube.music icon drawable to bitmap
07-05 14:58:06.225   336 11875 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:06.231 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.248 11802 11866 I APP:ScanAppCallable: converting com.google.android.calendar icon drawable to bitmap
07-05 14:58:06.256 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.263   398  1303 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10204 pid=0
07-05 14:58:06.263   398  1303 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10204 => denied (580 us)
07-05 14:58:06.264   398  1303 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10204 pid=0
07-05 14:58:06.264   398   778 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10204 pid=11802
07-05 14:58:06.264   398  1303 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10204 => denied (534 us)
07-05 14:58:06.264   398   778 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10204 => denied (963 us)
07-05 14:58:06.264   398  1303 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10204 pid=0
07-05 14:58:06.264   398   778 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10204 pid=11802
07-05 14:58:06.264   398  1303 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10204 => denied (138 us)
07-05 14:58:06.264   398   778 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10204 => denied (143 us)
07-05 14:58:06.275 11802 11856 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:58:06.281 11802 11802 E RecyclerView: No adapter attached; skipping layout
07-05 14:58:06.282 11802 11866 I APP:ScanAppCallable: converting com.google.android.contacts icon drawable to bitmap
07-05 14:58:06.287   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4710
07-05 14:58:06.287   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d18f0
07-05 14:58:06.287 11802 11856 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 14:58:06.288 11802 11856 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 14:58:06.294 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.308 11802 11856 D EGL_emulation: eglCreateContext: 0x720c5f5e35d0: maj 3 min 1 rcv 4
07-05 14:58:06.319   796   966 D EGL_emulation: app_time_stats: avg=6260.51ms min=6260.51ms max=6260.51ms count=1
07-05 14:58:06.329 11802 11866 I APP:ScanAppCallable: converting com.google.android.deskclock icon drawable to bitmap
07-05 14:58:06.347 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.363 11802 11856 D EGL_emulation: eglMakeCurrent: 0x720c5f5e35d0: ver 3 1 (tinfo 0x720e7729f080) (first time)
07-05 14:58:06.367 11802 11866 I APP:ScanAppCallable: converting com.google.android.dialer icon drawable to bitmap
07-05 14:58:06.398   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 14:58:06.398 11802 11856 I Gralloc4: mapper 4.x is not supported
07-05 14:58:06.402 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.402   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 14:58:06.403   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 14:58:06.404 11802 11856 W Gralloc4: allocator 4.x is not supported
07-05 14:58:06.417 11802 11866 I APP:ScanAppCallable: converting com.google.android.gm icon drawable to bitmap
07-05 14:58:06.421 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.431 11802 11856 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:58:06.432 11802 11856 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:58:06.440 11802 11866 I APP:ScanAppCallable: converting com.google.android.youtube icon drawable to bitmap
07-05 14:58:06.458 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.474 11802 11866 I APP:ScanAppCallable: converting com.android.stk icon drawable to bitmap
07-05 14:58:06.478 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.491 11802 11866 I APP:ScanAppCallable: converting com.google.android.documentsui icon drawable to bitmap
07-05 14:58:06.500 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.512 11802 11866 I APP:ScanAppCallable: converting com.google.android.googlequicksearchbox icon drawable to bitmap
07-05 14:58:06.516 11802 11866 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:58:06.522   564   602 W ziparchive: Unable to open '/data/app/~~NZYlKBQffk-KJ3MX7-VI1A==/com.kewtoms.whatappsdo-swlBgQ1ednW5YKAcyhhfMA==/base.dm': No such file or directory
07-05 14:58:06.523   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +2s816ms
07-05 14:58:06.534 11802 11860 D APP:AppScraper: checkAppNeedScrape: disabled
07-05 14:58:06.534 11802 11860 D APP:AppScraper: scrapeWhenNeeded: disabled
07-05 14:58:06.535 11802 11860 D APP:AppScraper: sendScrapeData: disabled
07-05 14:58:06.535 11802 11860 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 14:58:06.550   564  2201 W InputManager-JNI: Input channel object '6bfb056 Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 14:58:06.558 11802 11802 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:58:06.558 11802 11802 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:58:06.559 11802 11802 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 14:58:06.559 11802 11802 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 14:58:06.564 11802 11802 I APP:Authenticator: silentSignIn: Validate success. Signing in..
07-05 14:58:06.565 11802 11802 D APP:Authenticator: signInUsingAccessToken:run
07-05 14:58:06.566 11802 11802 I APP:Authenticator: signInUsingAccessToken:: Sending login request:http://localhost:8000/__mock_sign_in_access_token
07-05 14:58:06.567 11802 11802 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:58:06.570 11802 11802 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:58:06.572 11802 11877 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:58:06.573 11802 11877 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:58:06.575 11802 11877 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:58:06.576 11802 11802 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:58:06.576 11802 11802 D APP:Authenticator: signInUsingAccessToken:end
07-05 14:58:06.576 11802 11802 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 14:58:06.580   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2e50
07-05 14:58:06.580   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5430
07-05 14:58:06.580   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d30f0
07-05 14:58:06.580   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d30f0
07-05 14:58:06.580   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d30f0
07-05 14:58:06.580   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5430
07-05 14:58:06.580 11802 11882 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:58:06.580 11802 11882 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:06.584  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 14:58:06.585  1166  1735 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef609860 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 14:58:06.588 11802 11882 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:06.608 11802 11802 D InsetsController: show(ime(), fromIme=false)
07-05 14:58:06.624  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.624  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.624  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null}, false)
07-05 14:58:06.626  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:58:06.627  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 14:58:06.627  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.631  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 14:58:06.631  1371  1371 I AndroidIME: AbstractIme.onActivate():86 PasswordIme.onActivate() : EditorInfo = inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 14:58:06.637  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 14:58:06.646  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:58:06.646  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY
07-05 14:58:06.647  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:06.647  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 14:58:06.648  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:06.649  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 14:58:06.649  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 14:58:06.650  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:58:06.652  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.654  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.655  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.655  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 14:58:06.655  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:58:06.656  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 14:58:06.657  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:58:06.658  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 14:58:06.658  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: AutoPasteSuggestionHelper.createProactiveSuggestions():382 Failed to create item chips. Clip items are [ClipItem{ id = 1751727377293, timestamp = 1751727377295, clipItemContent = ClipItemContent{text=
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: ... 33 trimmed
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: Caused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: at android.content.res.AssetManager.nativeOpenAsset(Native Method)
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: at android.content.res.AssetManager.open(AssetManager.java:904)
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: at android.content.res.AssetManager.open(AssetManager.java:881)
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: ... 35 more
07-05 14:58:06.661  1371  1371 W AutoPasteSuggestionHelper: , htmlText=, itemType=0, entityType=0, uri=, groupId=-1, viewType=0}}, ClipItem{ id = 1751727377294, timestamp = 1751727377295, clipItemContent = ClipItemContent{text=33, htmlText=, itemType=0, entityType=4, uri=, groupId=-1, viewType=0}}, ClipItem{ id = 1751727377295, timestamp = 1751727377295, clipItemContent = ClipItemContent{text=35, htmlText=, itemType=0, entityType=4, uri=, groupId=-1, viewType=0}}].
07-05 14:58:06.662  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 14:58:06.665  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 14:58:06.666  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 14:58:06.666  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 PasswordIme.onDeactivate()
07-05 14:58:06.672 11802 11882 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:58:06.674  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 14:58:06.675  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 14:58:06.675  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 14:58:06.675  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 14:58:06.679   796   966 D EGL_emulation: app_time_stats: avg=2678.90ms min=2678.90ms max=2678.90ms count=1
07-05 14:58:06.682  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 14:58:06.684  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 14:58:06.684  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:58:06.687   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4cb0
07-05 14:58:06.689  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 14:58:06.690 11802 11802 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10204; state: ENABLED
07-05 14:58:06.691  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.691  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:58:06.692  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 14:58:06.694  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 14:58:06.694  1371  1371 I AndroidIME: AbstractIme.onActivate():86 LatinIme.onActivate() : EditorInfo = inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 14:58:06.695  1371  1371 I Delight5Facilitator: Delight5Facilitator.initializeForIme():777 initializeForIme() : Locale = [en_US], layout = qwerty
07-05 14:58:06.695 11802 11802 I AssistStructure: Flattened final assist data: 2432 bytes, containing 1 windows, 15 views
07-05 14:58:06.695   336 11886 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:06.698  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.cancelShutdown():55 cancelShutdown()
07-05 14:58:06.698  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.syncLanguagePacks():67 syncLanguagePacks()
07-05 14:58:06.701  1371 10352 I SpeechFactory: SpeechRecognitionFactory.maybeScheduleAutoPackDownloadForFallback():205 maybeScheduleAutoPackDownloadForFallback()
07-05 14:58:06.702  1371 10352 I FallbackOnDeviceRecognitionProvider: FallbackOnDeviceRecognitionProvider.maybeScheduleAutoPackDownload():195 maybeScheduleAutoPackDownload() for language tag en-US
07-05 14:58:06.704  1371  1371 I LatinIme: LatinIme.updateEnableInlineSuggestionsOnDecoderSideFlags():1003 inline flag updated to:false
07-05 14:58:06.707 11802 11882 E Volley  : [85] NetworkUtility.shouldRetryException: Unexpected response code 404 for http://localhost:8000/__mock_sign_in_access_token
07-05 14:58:06.710 11802 11802 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{2dcad7c VFED..CL. .F...... 265,536-815,683 #7f0801b2 app:id/search_field aid=**********} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 14:58:06.712  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 14:58:06.715 11802 11802 E APP:RequestUtils: getJsonObjectRequest.onErrorResponse: VolleyError: com.android.volley.ClientError
07-05 14:58:06.716 11802 11802 I APP:Authenticator: signInUsingAccessToken:: Login using access token failed
07-05 14:58:06.719  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.724  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 14:58:06.738  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 14:58:06.738  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 14:58:06.743  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 14:58:06.757  1274  1287 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:58:06.758  1274  1287 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:58:06.761  1274  1287 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:58:06.763  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:58:06.767  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.771  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.772  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_OPTIONAL
07-05 14:58:06.773  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:1729283189
07-05 14:58:06.773  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:06.773  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 14:58:06.774  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:06.774  1274  1290 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef61f4c0 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 14:58:06.775  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 14:58:06.775  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 14:58:06.777  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:58:06.781  1274  1290 I FontLog : Pulling font file for id = 47, cache size = 8 [CONTEXT service_id=132 ]
07-05 14:58:06.781  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.782  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.784  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.785  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 14:58:06.786  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:58:06.788  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 14:58:06.789  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:58:06.790  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 14:58:06.790  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 14:58:06.797  1274  1290 I FontLog : Pulling font file for id = 47, cache size = 8 [CONTEXT service_id=132 ]
07-05 14:58:06.803  1371  1371 I ProactiveSuggestionsHolderManager: ProactiveSuggestionsHolderManager$3.display():216 Requesting to show proactive suggestions: CLIPBOARD PREEMPTIVE_WITH_SUPPRESSION
07-05 14:58:06.809  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 14:58:06.819  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.823  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:06.917 11802 11825 W FileTestStorage: Output properties is not supported.
07-05 14:58:06.921 11802 11825 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 14:58:06.949 11802 11825 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 14:58:06.949 11802 11825 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 14:58:06.950 11802 11825 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 14:58:06.950 11802 11825 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 14:58:06.967  1371  1939 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:58:07.008 11802 11825 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 14:58:07.027  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:07.030  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:07.033 11802 11802 D InsetsController: show(ime(), fromIme=true)
07-05 14:58:07.035  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:07.038  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:58:07.054 11802 11802 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:58:07.054 11802 11802 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:58:07.054 11802 11802 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 14:58:07.055 11802 11802 D InsetsController: show(ime(), fromIme=true)
07-05 14:58:07.061  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 14:58:07.065  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 14:58:07.081  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.082  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.092  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.109 11802 11802 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 14:58:07.110 11802 11802 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:58:07.111 11802 11802 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:58:07.114  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.114  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.124  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.130  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.130  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.134 11802 11802 I ViewInteraction: Performing 'type text(nonexistentapp)' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 14:58:07.135  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.152  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7878148, downTime=7878148, deviceId=-1, source=0x1002, displayId=0, eventId=-146646122 }
07-05 14:58:07.154  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.155  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.158  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:58:07.190  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7878189, downTime=7878148, deviceId=-1, source=0x1002, displayId=0, eventId=-1057175875 }
07-05 14:58:07.191 11802 11802 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{2dcad7c VFED..CL. .F.P..ID 265,126-815,273 #7f0801b2 app:id/search_field aid=**********} flags=0 reason=SHOW_SOFT_INPUT
07-05 14:58:07.215  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:58:07.219 11802 11802 D InsetsController: show(ime(), fromIme=true)
07-05 14:58:07.425 11802 11802 D UiControllerImpl: Injecting string: "nonexistentapp"
07-05 14:58:07.431   564  2201 D InputDispatcher: Touch mode switch rejected, caller (pid=0, uid=10204) doesn't own the focused window nor none of the previously interacted window
07-05 14:58:07.465  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.465  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.465  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.515  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.515  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.518  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.545  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.545  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.546  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.558  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.558  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.559  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.602  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.603  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.604  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.606 11802 11856 D EGL_emulation: app_time_stats: avg=74.83ms min=5.77ms max=360.68ms count=14
07-05 14:58:07.631  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.631  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.632  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.662  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.662  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.663  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.693  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.693  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.694  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.731  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.731  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.732  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.760  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.760  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.762  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.794  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.794  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.795  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.829  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.830  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.831  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.865  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:58:07.865  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:58:07.866  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:58:07.871 11802 11802 I ViewInteraction: Performing 'close keyboard' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 14:58:07.874 11802 11802 D IdlingRegistry: Registering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@206138e]
07-05 14:58:07.876  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 14:58:07.878  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 14:58:07.883  1371  1371 I InputBundle: InputBundle.consumeEvent():923 Skip consuming an event as keyboard status is 0
07-05 14:58:07.885  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 14:58:07.886  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.shutdown():77 shutdown()
07-05 14:58:07.887  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 LatinIme.onDeactivate()
07-05 14:58:07.898  1371  2057 I native  : I0000 00:00:1751727487.898380    2057 input-context-store.cc:255] Ignoring stale client request for FetchSuggestions
07-05 14:58:07.902  1371  2057 I native  : I0000 00:00:1751727487.902451    2057 input-context-store.cc:178] Ignoring stale client request for OverrideDecodedCandidates
07-05 14:58:07.903  1371  1872 E native  : E0000 00:00:1751727487.902525    1872 keyboard.cc:27] Cannot create a keyboard with 0 valid keys
07-05 14:58:07.905  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 60<61, inputStateId=0, lastInputStateId=47
07-05 14:58:07.940   796   966 D EGL_emulation: app_time_stats: avg=630.01ms min=548.08ms max=711.93ms count=2
07-05 14:58:08.121   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d07b0
07-05 14:58:08.210 11802 11802 D IdlingRegistry: Unregistering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@206138e]
07-05 14:58:08.214 11802 11802 I ViewInteraction: Performing 'single click' action on view view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>
07-05 14:58:08.216  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7879214, downTime=7879214, deviceId=-1, source=0x1002, displayId=0, eventId=-159042483 }
07-05 14:58:08.258  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7879257, downTime=7879214, deviceId=-1, source=0x1002, displayId=0, eventId=-393757419 }
07-05 14:58:08.273 11802 11802 I APP:HomeFragment: onCreateView: searchButton onClick
07-05 14:58:08.276 11802 11802 I APP:HomeFragment: onCreateView: userUsageCount: 0
07-05 14:58:08.278 11802 11802 I APP:AppSearcher: Searching for app: nonexistentapp
07-05 14:58:08.281 11802 11802 I APP:AppSearcher: jsonBody:{"data_str":"{\"allowSearch\":true,\"appIds\":[\"com.android.camera2\",\"com.android.chrome\",\"com.android.settings\",\"com.google.android.apps.docs\",\"com.google.android.apps.maps\",\"com.google.android.apps.messaging\",\"com.google.android.apps.photos\",\"com.google.android.apps.youtube.music\",\"com.google.android.calendar\",\"com.google.android.contacts\",\"com.google.android.deskclock\",\"com.google.android.dialer\",\"com.google.android.gm\",\"com.google.android.youtube\",\"com.android.stk\",\"com.google.android.documentsui\",\"com.google.android.googlequicksearchbox\"],\"query\":\"nonexistentapp\"}"}
07-05 14:58:08.281 11802 11802 I APP:AppSearcher: searchApp: Sending request to:http://localhost:8000/__mock_search_user_app
07-05 14:58:08.281 11802 11802 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:58:08.282 11802 11802 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:58:08.282 11802 11892 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:58:08.284 11802 11892 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:58:08.285   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(35)
07-05 14:58:08.287 11802 11892 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:58:08.292 11802 11802 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:58:08.294 11802 11895 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:58:08.295 11802 11895 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:58:08.295 11802 11802 I APP:HomeFragment: Hided soft keyboard
07-05 14:58:08.300 11802 11895 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:58:08.337 11802 11895 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:58:08.342   336 11900 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:08.346   336 11901 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10204 0}
07-05 14:58:08.354 11802 11802 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:58:08.354 11802 11802 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:58:08.354 11802 11802 I APP:AppSearcher: onPostSuccess: responseJsonObj:{"code":400,"is_success":false,"message":"Operation failed.","data":{},"error":"Operation failed."}
07-05 14:58:08.355 11802 11802 D CompatibilityChangeReporter: Compat change id reported: 147798919; UID 10204; state: ENABLED
07-05 14:58:08.380   796   966 W Parcel  : Expecting binder but got null!
07-05 14:58:08.420   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:58:08.549 11802 11903 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 14:58:08.906 11802 11856 D EGL_emulation: app_time_stats: avg=84.94ms min=2.91ms max=491.21ms count=15
07-05 14:58:10.405 11802 11856 D EGL_emulation: app_time_stats: avg=499.82ms min=499.53ms max=500.02ms count=3
07-05 14:58:10.725   564   593 W InputManager-JNI: Input channel object '67577b Toast (client)' was disposed without first being removed with the input manager!
07-05 14:58:10.736   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d1590
07-05 14:58:10.736   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d27f0
07-05 14:58:10.736   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4ad0
07-05 14:58:10.737   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d27f0
07-05 14:58:11.406 11802 11856 D EGL_emulation: app_time_stats: avg=500.48ms min=499.67ms max=501.29ms count=2
07-05 14:58:11.534 11802 11802 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)}' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 14:58:11.540 11802 11802 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>}' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 14:58:11.567 11802 11802 D takeScreenshot: Found 1 global views to redraw
07-05 14:58:11.576 11802 11811 W toms.whatappsdo: Cleared Reference was only reachable from finalizer (only reported once)
07-05 14:58:11.595 11802 11813 W System  : A resource failed to call close.
07-05 14:58:11.595 11802 11813 W System  : A resource failed to call close.
07-05 14:58:11.597 11802 11813 W System  : A resource failed to call close.
07-05 14:58:11.635   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 14:58:11.635 11788 11829 I Gralloc4: mapper 4.x is not supported
07-05 14:58:11.670 11788 11829 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:58:11.677 11788 11905 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 14:58:11.678 11788 11905 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 14:58:11.679 11788 11905 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 14:58:11.703 11788 11905 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:58:11.704 11788 11905 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 14:58:11.705 11788 11905 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 14:58:11.727 11788 11905 D EGL_emulation: eglCreateContext: 0x73d86a8d2150: maj 3 min 1 rcv 4
07-05 14:58:11.759 11788 11905 D EGL_emulation: eglMakeCurrent: 0x73d86a8d2150: ver 3 1 (tinfo 0x73da8c835080) (first time)
07-05 14:58:11.875  1525  1712 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 14:58:11.940  1274  1274 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 14:58:11.940  1274  1274 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 14:58:11.941  1274 11642 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService started execution. cause:9 exec_start_elapsed_seconds: 7882 [CONTEXT service_id=218 ]
07-05 14:58:11.944  1274 11642 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService finished executing. cause:9 result: 1 elapsed_millis: 13 uptime_millis: 13 exec_start_elapsed_seconds: 7882 [CONTEXT service_id=218 ]
07-05 14:58:11.983 11802 11825 E TestRunner: failed: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 14:58:11.983 11802 11825 E TestRunner: ----- begin exception -----
07-05 14:58:11.988 11802 11825 E TestRunner: androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
07-05 14:58:11.988 11802 11825 E TestRunner: Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
07-05 14:58:11.988 11802 11825 E TestRunner:      Got: was <androidx.recyclerview.widget.RecyclerView{c06cd81 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
07-05 14:58:11.988 11802 11825 E TestRunner: View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}
07-05 14:58:11.988 11802 11825 E TestRunner:
07-05 14:58:11.988 11802 11825 E TestRunner: 	at dalvik.system.VMStack.getThreadStackTrace(Native Method)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at java.lang.Thread.getStackTrace(Thread.java:1841)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:262)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:58:11.988 11802 11825 E TestRunner: Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
07-05 14:58:11.988 11802 11825 E TestRunner: Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
07-05 14:58:11.988 11802 11825 E TestRunner:      Got: was <androidx.recyclerview.widget.RecyclerView{c06cd81 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
07-05 14:58:11.988 11802 11825 E TestRunner: View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}
07-05 14:58:11.988 11802 11825 E TestRunner:
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at android.os.Looper.loop(Looper.java:288)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 14:58:11.988 11802 11825 E TestRunner: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 14:58:11.988 11802 11825 E TestRunner: ----- end exception -----
07-05 14:58:11.999 11802 11825 I TestRunner: finished: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
