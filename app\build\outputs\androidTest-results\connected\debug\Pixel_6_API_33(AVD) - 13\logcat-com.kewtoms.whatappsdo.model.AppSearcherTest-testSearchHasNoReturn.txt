07-05 15:18:14.481 13441 13464 I TestRunner: started: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 15:18:14.492  1543  1736 I AiAiEcho: Predicting[0]:
07-05 15:18:14.499  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 15:18:14.503  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:18:14.505  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:18:14.508  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:18:14.510  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:18:14.510  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:18:14.510 13441 13464 D APP:SecurePrefsManager: saveSignInData: run
07-05 15:18:14.510 13441 13464 D APP:SecurePrefsManager: saveAccessToken: run
07-05 15:18:14.511  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:18:14.511 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:14.517  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:18:14.518  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:18:14.519  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:18:14.525   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:18:14.528   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:18:14.529   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:18:14.540 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:185)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:18:14.842 13441 13464 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:185)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:18:14.911 13441 13464 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:18:14.936 13441 13464 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 15:18:14.947 13441 13464 D APP:SecurePrefsManager: saveAccessToken: done
07-05 15:18:14.948 13441 13464 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 15:18:14.948 13441 13464 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 15:18:14.948 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:14.952 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:14.983 13441 13464 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 15:18:14.983 13441 13464 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 15:18:14.983 13441 13464 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 15:18:14.983 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:14.986 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:15.017 13441 13464 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 15:18:15.017 13441 13464 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 15:18:15.017 13441 13464 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 15:18:15.017 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:15.020 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:15.052 13441 13464 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 15:18:15.053 13441 13464 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 15:18:15.053 13441 13464 D APP:SecurePrefsManager: saveLoginTime: run
07-05 15:18:15.053 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:15.056 13441 13464 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:15.086 13441 13464 D APP:SecurePrefsManager: saveLoginTime: done
07-05 15:18:15.086 13441 13464 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 15:18:15.086 13441 13464 D APP:SecurePrefsManager: saveSignInData: done
07-05 15:18:15.291   336 13492 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:15.292 13441 13464 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 15:18:15.310   336 13494 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:15.329 13441 13464 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 15:18:15.366   796   930 D SplashScreenView: Build android.window.SplashScreenView{511c0ed V.E...... ......ID 0,0-0,0}
07-05 15:18:15.366   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 15:18:15.366   796   930 D SplashScreenView: Branding: view: android.view.View{5a58b22 G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 15:18:15.373   796   966 W Parcel  : Expecting binder but got null!
07-05 15:18:15.383  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 15:18:15.384 13441 13496 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 15:18:15.390 13441 13496 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 15:18:15.393 13441 13496 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 15:18:15.429   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:18:15.430  1166  1782 D EGL_emulation: app_time_stats: avg=2095.54ms min=537.08ms max=3654.01ms count=2
07-05 15:18:15.437   564  1117 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 15:18:15.437   564  1117 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 15:18:15.437   564  1117 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 15:18:15.437   564  1117 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 15:18:15.437   564  1117 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 15:18:15.437   564  1117 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 15:18:15.437   564  1117 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 15:18:15.437   564  1117 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 15:18:15.562 13441 13441 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 15:18:15.575 13441 13441 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 15:18:15.576 13441 13441 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 15:18:15.652   796   966 D EGL_emulation: app_time_stats: avg=34282.28ms min=929.63ms max=67634.92ms count=2
07-05 15:18:15.686   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4110
07-05 15:18:15.715 13441 13441 D APP:MainActivity: onCreate: run
07-05 15:18:15.717 13441 13441 D APP:Constants: initializeData: run
07-05 15:18:15.719 13441 13441 D APP:Constants: initializeData: done
07-05 15:18:15.970   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3990
07-05 15:18:16.381 13441 13441 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10212; state: ENABLED
07-05 15:18:16.418 13441 13441 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 15:18:16.418 13441 13441 I APP:MainActivity: onCreate: done setting root view
07-05 15:18:16.443 13441 13441 I APP:MainActivity: onCreate: Done initializing drawer
07-05 15:18:16.502 13441 13441 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 15:18:17.053 13441 13441 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 264.256ms (1052.01 bytecodes/s) (8240B approximate peak alloc)
07-05 15:18:17.092 13441 13441 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 15:18:17.092 13441 13441 D APP:MainActivity: onCreate: Done
07-05 15:18:17.093 13441 13441 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 15:18:17.094 13441 13441 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:18:17.097 13441 13441 D APP:HomeFragment: onCreateView: run
07-05 15:18:17.191 13441 13441 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 15:18:17.192 13441 13441 I CodeRunManager: Feature HomeFragment.runThreadCheckAndCachePackagesRelated not found in config. Using default value: true
07-05 15:18:17.192 13441 13441 I APP:HomeFragment: silentSignIn: run
07-05 15:18:17.193 13441 13500 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: Cache directories created successfully
07-05 15:18:17.390 13441 13441 W toms.whatappsdo: Verification of void com.kewtoms.whatappsdo.model.auth.Authenticator.obtainVerificationCode(java.lang.String, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, boolean) took 107.519ms (1478.80 bytecodes/s) (3992B approximate peak alloc)
07-05 15:18:17.494 13441 13500 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 246.020ms (130.07 bytecodes/s) (2528B approximate peak alloc)
07-05 15:18:17.745 13441 13500 I APP:HomeFragment: sending get request to get_is_server_online_link: http://localhost:8000/__mock_get_is_server_online
07-05 15:18:17.779 13441 13441 D APP:Authenticator: silentSignIn: run
07-05 15:18:17.779 13441 13441 D APP:Authenticator: hasAccountStored: run
07-05 15:18:17.779 13441 13441 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:18:17.781 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:17.787 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:17.828   336 13501 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:17.829 13441 13500 D TrafficStats: tagSocket(103) with statsTag=0xffffffff, statsUid=-1
07-05 15:18:17.832 13441 13493 D TrafficStats: tagSocket(104) with statsTag=0xffffffff, statsUid=-1
07-05 15:18:17.853 13441 13441 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:18:17.853 13441 13441 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:18:17.853 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:17.856 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:17.872   336 13505 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:17.907 13441 13441 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:18:17.907 13441 13441 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:18:17.907 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:17.910 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:17.955 13441 13441 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:18:17.957 13441 13441 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 15:18:17.957 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:17.959 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:18.009 13441 13441 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 15:18:18.010 13441 13441 D APP:Authenticator: hasAccountStored: end: true
07-05 15:18:18.010 13441 13441 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:18:18.011 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:18.015 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:18.055 13441 13441 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:18:18.056 13441 13441 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:18:18.057 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:18.060 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:18.062 13441 13506 I APP:ScanAppCallable: run: pool-3-thread-1
07-05 15:18:18.066 13441 13506 I System.out: Directories created successfully
07-05 15:18:18.099 13441 13441 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:18:18.099 13441 13441 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:18:18.100 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:18.103 13441 13506 I APP:ScanAppCallable: converting com.android.camera2 icon drawable to bitmap
07-05 15:18:18.103 13441 13441 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:18.112 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.127 13441 13506 I APP:ScanAppCallable: converting com.android.chrome icon drawable to bitmap
07-05 15:18:18.137 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.139 13441 13441 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:18:18.151 13441 13506 I APP:ScanAppCallable: converting com.android.settings icon drawable to bitmap
07-05 15:18:18.161 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.176 13441 13506 I APP:ScanAppCallable: converting com.google.android.apps.docs icon drawable to bitmap
07-05 15:18:18.181 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.192 13441 13441 D APP:Authenticator: validateAccessToken: run
07-05 15:18:18.192 13441 13441 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 15:18:18.193 13441 13441 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:18:18.195 13441 13441 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:18:18.199 13441 13507 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:18:18.199 13441 13507 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:18:18.204 13441 13506 I APP:ScanAppCallable: converting com.google.android.apps.maps icon drawable to bitmap
07-05 15:18:18.216 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.232 13441 13506 I APP:ScanAppCallable: converting com.google.android.apps.messaging icon drawable to bitmap
07-05 15:18:18.247 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.250 13441 13507 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:18:18.251 13441 13441 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:18:18.251 13441 13441 D APP:Authenticator: validateAccessToken: end
07-05 15:18:18.252 13441 13441 D APP:Authenticator: silentSignIn: done
07-05 15:18:18.252 13441 13441 D APP:HomeFragment: silentSignIn: done
07-05 15:18:18.253 13441 13441 D APP:HomeFragment: onCreateView: done
07-05 15:18:18.260 13441 13511 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:18:18.261 13441 13511 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:18.263 13441 13506 I APP:ScanAppCallable: converting com.google.android.apps.photos icon drawable to bitmap
07-05 15:18:18.263 13441 13511 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:18.265 13441 13441 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 15:18:18.266 13441 13441 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:18:18.271 13441 13441 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 15:18:18.271 13441 13441 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:18:18.274 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.279 13441 13441 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10212; state: DISABLED
07-05 15:18:18.287 13441 13495 W Parcel  : Expecting binder but got null!
07-05 15:18:18.293 13441 13506 I APP:ScanAppCallable: converting com.google.android.apps.youtube.music icon drawable to bitmap
07-05 15:18:18.302 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.323 13441 13506 I APP:ScanAppCallable: converting com.google.android.calendar icon drawable to bitmap
07-05 15:18:18.332 13441 13511 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:18:18.341 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.342   336 13515 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:18.357 13441 13506 I APP:ScanAppCallable: converting com.google.android.contacts icon drawable to bitmap
07-05 15:18:18.361   398   778 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10212 pid=0
07-05 15:18:18.361   398   778 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10212 => denied (1259 us)
07-05 15:18:18.362   398   778 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10212 pid=0
07-05 15:18:18.362   398   778 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10212 => denied (457 us)
07-05 15:18:18.362   398  1303 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10212 pid=13441
07-05 15:18:18.362   398  1303 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10212 => denied (400 us)
07-05 15:18:18.362   398   778 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10212 pid=0
07-05 15:18:18.362   398   778 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10212 => denied (362 us)
07-05 15:18:18.363   398  1303 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10212 pid=13441
07-05 15:18:18.363   398  1303 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10212 => denied (774 us)
07-05 15:18:18.368 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.378 13441 13495 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:18:18.379 13441 13441 E RecyclerView: No adapter attached; skipping layout
07-05 15:18:18.386 13441 13495 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 15:18:18.386 13441 13495 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 15:18:18.406 13441 13506 I APP:ScanAppCallable: converting com.google.android.deskclock icon drawable to bitmap
07-05 15:18:18.410 13441 13495 D EGL_emulation: eglCreateContext: 0x720c5f5e2fd0: maj 3 min 1 rcv 4
07-05 15:18:18.419   398   429 W TransactionTracing: Could not find layer handle 0x71102f7cf610
07-05 15:18:18.419   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d55b0
07-05 15:18:18.441 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.458   796   966 D EGL_emulation: app_time_stats: avg=13384.03ms min=13384.03ms max=13384.03ms count=1
07-05 15:18:18.465 13441 13495 D EGL_emulation: eglMakeCurrent: 0x720c5f5e2fd0: ver 3 1 (tinfo 0x720e77346080) (first time)
07-05 15:18:18.469 13441 13506 I APP:ScanAppCallable: converting com.google.android.dialer icon drawable to bitmap
07-05 15:18:18.521   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 15:18:18.521 13441 13495 I Gralloc4: mapper 4.x is not supported
07-05 15:18:18.526 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.528   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 15:18:18.530   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 15:18:18.531 13441 13495 W Gralloc4: allocator 4.x is not supported
07-05 15:18:18.540 13441 13506 I APP:ScanAppCallable: converting com.google.android.gm icon drawable to bitmap
07-05 15:18:18.548 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.559 13441 13495 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:18:18.560 13441 13495 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:18:18.574 13441 13506 I APP:ScanAppCallable: converting com.google.android.youtube icon drawable to bitmap
07-05 15:18:18.586 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.602 13441 13506 I APP:ScanAppCallable: converting com.android.stk icon drawable to bitmap
07-05 15:18:18.607 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.621 13441 13506 I APP:ScanAppCallable: converting com.google.android.documentsui icon drawable to bitmap
07-05 15:18:18.635 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.648   564   602 W ziparchive: Unable to open '/data/app/~~15Bb3ZlEPlYQMpfja9RJfg==/com.kewtoms.whatappsdo-BF1sAPltxx8CqJQJJ5ijzQ==/base.dm': No such file or directory
07-05 15:18:18.649 13441 13506 I APP:ScanAppCallable: converting com.google.android.googlequicksearchbox icon drawable to bitmap
07-05 15:18:18.650   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +3s308ms
07-05 15:18:18.655 13441 13506 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:18:18.668   564  1117 W InputManager-JNI: Input channel object '969dade Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 15:18:18.676  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 15:18:18.677  1166  2106 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef625e50 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 15:18:18.679 13441 13500 D APP:AppScraper: checkAppNeedScrape: disabled
07-05 15:18:18.679 13441 13500 D APP:AppScraper: scrapeWhenNeeded: disabled
07-05 15:18:18.680 13441 13500 D APP:AppScraper: sendScrapeData: disabled
07-05 15:18:18.680 13441 13500 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 15:18:18.691   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d39f0
07-05 15:18:18.691   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d57f0
07-05 15:18:18.691   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0ab0
07-05 15:18:18.692   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0ab0
07-05 15:18:18.692   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0ab0
07-05 15:18:18.692   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d57f0
07-05 15:18:18.692   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d39f0
07-05 15:18:18.698 13441 13441 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:18:18.698 13441 13441 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:18:18.698 13441 13441 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 15:18:18.698 13441 13441 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 15:18:18.698 13441 13441 I APP:Authenticator: silentSignIn: Validate success. Signing in..
07-05 15:18:18.699 13441 13441 D APP:Authenticator: signInUsingAccessToken:run
07-05 15:18:18.700 13441 13441 I APP:Authenticator: signInUsingAccessToken:: Sending login request:http://localhost:8000/__mock_sign_in_access_token
07-05 15:18:18.701 13441 13441 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:18:18.702 13441 13441 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:18:18.702 13441 13518 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:18:18.702 13441 13518 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:18:18.706 13441 13518 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:18:18.707 13441 13521 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:18:18.707 13441 13521 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:18.711 13441 13521 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:18.711 13441 13441 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:18:18.712 13441 13441 D APP:Authenticator: signInUsingAccessToken:end
07-05 15:18:18.713 13441 13441 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 15:18:18.738  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:608733072
07-05 15:18:18.752 13441 13441 D InsetsController: show(ime(), fromIme=false)
07-05 15:18:18.764  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.764  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.765  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null}, false)
07-05 15:18:18.767  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.768  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:18:18.769  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:18:18.773  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:18:18.774  1371  1371 I AndroidIME: AbstractIme.onActivate():86 PasswordIme.onActivate() : EditorInfo = inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:18:18.780  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:18:18.794  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:18:18.795  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY
07-05 15:18:18.795   796   966 D EGL_emulation: app_time_stats: avg=3142.46ms min=3142.46ms max=3142.46ms count=1
07-05 15:18:18.795  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:18.796  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:18:18.796  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:18.797  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:18:18.797  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:18:18.798  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:18:18.799  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.801  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.801  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.801  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:18:18.801  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:18:18.802  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:18:18.803 13441 13521 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:18:18.808   336 13525 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:18.813  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:18:18.813 13441 13521 E Volley  : [83] NetworkUtility.shouldRetryException: Unexpected response code 404 for http://localhost:8000/__mock_sign_in_access_token
07-05 15:18:18.813  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:18:18.814  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:18:18.821  1371  1371 W AutoPasteSuggestionHelper: AutoPasteSuggestionHelper.createProactiveSuggestions():382 Failed to create item chips. Clip items are [ClipItem{ id = 1751728645174, timestamp = 1751728645174, clipItemContent = ClipItemContent{text=RecyclerView , htmlText=, itemType=0, entityType=0, uri=, groupId=-1, viewType=0}}].
07-05 15:18:18.823  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:18:18.827  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:18:18.827  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:18:18.833  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:18:18.833  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:18:18.833 13441 13441 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10212; state: ENABLED
07-05 15:18:18.833  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 PasswordIme.onDeactivate()
07-05 15:18:18.837  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 15:18:18.838  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 15:18:18.841 13441 13441 I AssistStructure: Flattened final assist data: 2432 bytes, containing 1 windows, 15 views
07-05 15:18:18.842  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:18:18.847 13441 13441 E APP:RequestUtils: getJsonObjectRequest.onErrorResponse: VolleyError: com.android.volley.ClientError
07-05 15:18:18.847 13441 13441 I APP:Authenticator: signInUsingAccessToken:: Login using access token failed
07-05 15:18:18.857 13441 13441 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F...... 265,536-815,683 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 15:18:18.861   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d49b0
07-05 15:18:18.862  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 15:18:18.867  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:18:18.872  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:18:18.873  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:18:18.874  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:18:18.877  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:18:18.880  1371  1371 I AndroidIME: AbstractIme.onActivate():86 LatinIme.onActivate() : EditorInfo = inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:18:18.882  1371  1371 I Delight5Facilitator: Delight5Facilitator.initializeForIme():777 initializeForIme() : Locale = [en_US], layout = qwerty
07-05 15:18:18.884  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.cancelShutdown():55 cancelShutdown()
07-05 15:18:18.885  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.syncLanguagePacks():67 syncLanguagePacks()
07-05 15:18:18.888  1371 10352 I SpeechFactory: SpeechRecognitionFactory.maybeScheduleAutoPackDownloadForFallback():205 maybeScheduleAutoPackDownloadForFallback()
07-05 15:18:18.889  1371 10352 I FallbackOnDeviceRecognitionProvider: FallbackOnDeviceRecognitionProvider.maybeScheduleAutoPackDownload():195 maybeScheduleAutoPackDownload() for language tag en-US
07-05 15:18:18.891  1371  1371 I LatinIme: LatinIme.updateEnableInlineSuggestionsOnDecoderSideFlags():1003 inline flag updated to:false
07-05 15:18:18.898  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:18:18.898  1274  2762 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 15:18:18.898  1274  2762 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 15:18:18.899  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.900  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 15:18:18.900  1274  2762 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 15:18:18.909  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.912  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:18:18.913  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:18:18.919  1274  3332 I FontLog : Pulling font file for id = 54, cache size = 6 [CONTEXT service_id=132 ]
07-05 15:18:18.926  1274  3332 I FontLog : Pulling font file for id = 54, cache size = 6 [CONTEXT service_id=132 ]
07-05 15:18:18.949  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:18:18.960  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:647225256
07-05 15:18:18.966  1274  3332 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef6484a0 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 15:18:18.979  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:18:18.981  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.986  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.987  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_OPTIONAL
07-05 15:18:18.987  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:18.987  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:18:18.989  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:18.989  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:18:18.992  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:18:18.993  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:18:18.996  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.998  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:18.999  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:19.000  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:18:19.001  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:18:19.004  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:18:19.004  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:18:19.006  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:18:19.007  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:18:19.014  1371  1371 I ProactiveSuggestionsHolderManager: ProactiveSuggestionsHolderManager$3.display():216 Requesting to show proactive suggestions: CLIPBOARD PREEMPTIVE_WITH_SUPPRESSION
07-05 15:18:19.022  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:18:19.023  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:19.024  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:19.030 13441 13464 W FileTestStorage: Output properties is not supported.
07-05 15:18:19.036 13441 13464 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 15:18:19.091  1371  1939 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:18:19.096 13441 13464 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 15:18:19.097 13441 13464 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 15:18:19.098 13441 13464 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 15:18:19.098 13441 13464 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 15:18:19.142 13441 13441 D InsetsController: show(ime(), fromIme=true)
07-05 15:18:19.143  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:19.162  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:19.166  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:19.187 13441 13441 D InsetsController: show(ime(), fromIme=true)
07-05 15:18:19.193  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:18:19.195  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.197  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.199 13441 13464 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 15:18:19.210  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.217  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 15:18:19.218  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:18:19.223  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.225  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.255 13441 13441 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:18:19.258  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.263  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.263  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.270  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.273 13441 13441 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:18:19.274 13441 13441 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 15:18:19.285  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.285  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.292  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:18:19.308 13441 13441 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 15:18:19.308 13441 13441 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:18:19.309 13441 13441 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:18:19.329 13441 13441 I ViewInteraction: Performing 'type text(nonexistentapp)' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:18:19.341  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9090337, downTime=9090337, deviceId=-1, source=0x1002, displayId=0, eventId=-953405437 }
07-05 15:18:19.380  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9090379, downTime=9090337, deviceId=-1, source=0x1002, displayId=0, eventId=-75479167 }
07-05 15:18:19.381 13441 13441 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F.P..ID 265,126-815,273 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
07-05 15:18:19.383  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:18:19.389 13441 13441 D InsetsController: show(ime(), fromIme=true)
07-05 15:18:19.609 13441 13441 D UiControllerImpl: Injecting string: "nonexistentapp"
07-05 15:18:19.615   564  2524 D InputDispatcher: Touch mode switch rejected, caller (pid=0, uid=10212) doesn't own the focused window nor none of the previously interacted window
07-05 15:18:19.634 13441 13530 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 15:18:19.665  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.665  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.666  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.721  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.721  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.725  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.745 13441 13495 D EGL_emulation: app_time_stats: avg=98.46ms min=3.52ms max=314.57ms count=11
07-05 15:18:19.757  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.757  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.757  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.784  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.784  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.786  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.828  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.828  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.830  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.862  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.862  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.863  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.896  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.896  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.897  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.930  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.930  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.931  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.961  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.961  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.962  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:19.996  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:19.996  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:19.997  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:20.027  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:20.028  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:20.030  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:20.061  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:20.061  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:20.062  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:20.082  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:18:20.082  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:18:20.085  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 15:18:20.091 13441 13441 I ViewInteraction: Performing 'close keyboard' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:18:20.094 13441 13441 D IdlingRegistry: Registering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@206138e]
07-05 15:18:20.097  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:18:20.102  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:18:20.109  1371  1371 I InputBundle: InputBundle.consumeEvent():923 Skip consuming an event as keyboard status is 0
07-05 15:18:20.110  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:18:20.111  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.shutdown():77 shutdown()
07-05 15:18:20.111  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 LatinIme.onDeactivate()
07-05 15:18:20.117  1371  1872 E native  : E0000 00:00:1751728700.117283    1872 keyboard.cc:27] Cannot create a keyboard with 0 valid keys
07-05 15:18:20.126  1371  2057 I native  : I0000 00:00:1751728700.126224    2057 input-context-store.cc:255] Ignoring stale client request for FetchSuggestions
07-05 15:18:20.128  1371  2057 I native  : I0000 00:00:1751728700.128678    2057 input-context-store.cc:178] Ignoring stale client request for OverrideDecodedCandidates
07-05 15:18:20.133  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 131<132, inputStateId=0, lastInputStateId=115
07-05 15:18:20.223   796   966 D EGL_emulation: app_time_stats: avg=713.83ms min=438.67ms max=988.98ms count=2
07-05 15:18:20.352   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5610
07-05 15:18:20.446 13441 13441 D IdlingRegistry: Unregistering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@206138e]
07-05 15:18:20.450 13441 13441 I ViewInteraction: Performing 'single click' action on view view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>
07-05 15:18:20.453  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9091450, downTime=9091450, deviceId=-1, source=0x1002, displayId=0, eventId=-973783541 }
07-05 15:18:20.494  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9091493, downTime=9091450, deviceId=-1, source=0x1002, displayId=0, eventId=-303196940 }
07-05 15:18:20.508 13441 13441 I APP:HomeFragment: onCreateView: searchButton onClick
07-05 15:18:20.511 13441 13441 I APP:HomeFragment: onCreateView: userUsageCount: 0
07-05 15:18:20.513 13441 13441 I APP:AppSearcher: Searching for app: nonexistentapp
07-05 15:18:20.516 13441 13441 I APP:AppSearcher: jsonBody:{"data_str":"{\"allowSearch\":true,\"appIds\":[\"com.android.camera2\",\"com.android.chrome\",\"com.android.settings\",\"com.google.android.apps.docs\",\"com.google.android.apps.maps\",\"com.google.android.apps.messaging\",\"com.google.android.apps.photos\",\"com.google.android.apps.youtube.music\",\"com.google.android.calendar\",\"com.google.android.contacts\",\"com.google.android.deskclock\",\"com.google.android.dialer\",\"com.google.android.gm\",\"com.google.android.youtube\",\"com.android.stk\",\"com.google.android.documentsui\",\"com.google.android.googlequicksearchbox\"],\"query\":\"nonexistentapp\"}"}
07-05 15:18:20.516 13441 13441 I APP:AppSearcher: searchApp: Sending request to:http://localhost:8000/__mock_search_user_app
07-05 15:18:20.517   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(39)
07-05 15:18:20.519 13441 13441 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:18:20.519 13441 13441 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:18:20.522 13441 13534 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:18:20.525 13441 13534 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:18:20.527 13441 13534 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:18:20.529 13441 13441 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:18:20.531 13441 13441 I APP:HomeFragment: Hided soft keyboard
07-05 15:18:20.531 13441 13536 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:18:20.532 13441 13536 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:18:20.537 13441 13536 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:18:20.575 13441 13536 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:18:20.579   336 13541 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:20.581   336 13542 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10212 0}
07-05 15:18:20.582 13441 13504 I TEST_DEBUG: No results response: {"code":400,"is_success":false,"message":"Operation failed.","data":{},"error":"Operation failed."}
07-05 15:18:20.588 13441 13441 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:18:20.588 13441 13441 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:18:20.589 13441 13441 I APP:AppSearcher: onPostSuccess: responseJsonObj:{"code":400,"is_success":false,"message":"Operation failed.","data":{},"error":"Operation failed."}
07-05 15:18:20.592 13441 13441 D CompatibilityChangeReporter: Compat change id reported: 147798919; UID 10212; state: ENABLED
07-05 15:18:20.612   796   966 W Parcel  : Expecting binder but got null!
07-05 15:18:20.646   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:18:21.155 13441 13495 D EGL_emulation: app_time_stats: avg=73.13ms min=3.94ms max=494.00ms count=19
07-05 15:18:22.155 13441 13495 D EGL_emulation: app_time_stats: avg=499.82ms min=499.53ms max=500.11ms count=2
07-05 15:18:22.959   564  2524 W InputManager-JNI: Input channel object '84a6599 Toast (client)' was disposed without first being removed with the input manager!
07-05 15:18:22.969   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0c90
07-05 15:18:22.970   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d33f0
07-05 15:18:22.970   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3ab0
07-05 15:18:22.970   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d33f0
07-05 15:18:23.172 13441 13495 D EGL_emulation: app_time_stats: avg=508.66ms min=500.64ms max=516.68ms count=2
07-05 15:18:23.767 13441 13441 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)}' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 15:18:23.776 13441 13441 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>}' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 15:18:23.810 13441 13441 D takeScreenshot: Found 1 global views to redraw
07-05 15:18:23.820 13441 13450 W toms.whatappsdo: Cleared Reference was only reachable from finalizer (only reported once)
07-05 15:18:23.850 13441 13452 W System  : A resource failed to call close.
07-05 15:18:23.851 13441 13452 W System  : A resource failed to call close.
07-05 15:18:23.852 13441 13452 W System  : A resource failed to call close.
07-05 15:18:23.878   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 15:18:23.879 13425 13468 I Gralloc4: mapper 4.x is not supported
07-05 15:18:23.889 13425 13468 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:18:23.895 13425 13548 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 15:18:23.896 13425 13548 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 15:18:23.898 13425 13548 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 15:18:23.917 13425 13548 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:18:23.919 13425 13548 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 15:18:23.919 13425 13548 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 15:18:23.939 13425 13548 D EGL_emulation: eglCreateContext: 0x7c143708b690: maj 3 min 1 rcv 4
07-05 15:18:23.967 13425 13548 D EGL_emulation: eglMakeCurrent: 0x7c143708b690: ver 3 1 (tinfo 0x7c1666eb0080) (first time)
07-05 15:18:24.040  1525  1712 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 15:18:24.106  1274 13426 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService started execution. cause:9 exec_start_elapsed_seconds: 9095 [CONTEXT service_id=218 ]
07-05 15:18:24.112  1274  1274 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 15:18:24.112  1274  1274 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 15:18:24.114  1274 13164 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService finished executing. cause:9 result: 1 elapsed_millis: 19 uptime_millis: 19 exec_start_elapsed_seconds: 9095 [CONTEXT service_id=218 ]
07-05 15:18:24.154 13441 13464 E TestRunner: failed: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 15:18:24.155 13441 13464 E TestRunner: ----- begin exception -----
07-05 15:18:24.161 13441 13464 E TestRunner: androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
07-05 15:18:24.161 13441 13464 E TestRunner: Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
07-05 15:18:24.161 13441 13464 E TestRunner:      Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
07-05 15:18:24.161 13441 13464 E TestRunner: View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}
07-05 15:18:24.161 13441 13464 E TestRunner:
07-05 15:18:24.161 13441 13464 E TestRunner: 	at dalvik.system.VMStack.getThreadStackTrace(Native Method)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at java.lang.Thread.getStackTrace(Thread.java:1841)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:271)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:18:24.161 13441 13464 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:18:24.162 13441 13464 E TestRunner: Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
07-05 15:18:24.162 13441 13464 E TestRunner: Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
07-05 15:18:24.162 13441 13464 E TestRunner:      Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
07-05 15:18:24.162 13441 13464 E TestRunner: View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}
07-05 15:18:24.162 13441 13464 E TestRunner:
07-05 15:18:24.162 13441 13464 E TestRunner: 	at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at android.os.Looper.loop(Looper.java:288)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 15:18:24.162 13441 13464 E TestRunner: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 15:18:24.162 13441 13464 E TestRunner: ----- end exception -----
07-05 15:18:24.173 13441 13495 D EGL_emulation: app_time_stats: avg=333.39ms min=159.72ms max=498.32ms count=3
07-05 15:18:24.173 13441 13464 I TestRunner: finished: testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
