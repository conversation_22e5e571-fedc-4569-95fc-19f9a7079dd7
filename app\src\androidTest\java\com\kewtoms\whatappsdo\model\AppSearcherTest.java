package com.kewtoms.whatappsdo.model;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;
import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.hasMinimumChildCount;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static org.hamcrest.Matchers.not;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_get_is_server_online_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_obtain_new_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_search_user_app_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_validate_access_token_link;
import static com.kewtoms.whatappsdo.data.TestUtilsConstants.CODE_RUN_FOR_TEST_FOLDER_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.kewtoms.whatappsdo.MainActivity;
import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScrapeData;
import com.kewtoms.whatappsdo.model.scrapeApps.AppScraper;
import com.kewtoms.whatappsdo.utils.CodeRunManager;
import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
import com.kewtoms.whatappsdo.utils.TestUtils;

import org.json.JSONObject;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;


@RunWith(AndroidJUnit4.class)

public class AppSearcherTest {

  @BeforeClass
  public static void disableAnimations() {
    // Disable all animation scales to ensure test stability
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global window_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global transition_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global animator_duration_scale 0");
  }

  @Test
  public void testSearchHasReturn()
    throws
    IOException {

    // Get method name for CodeRunManager
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Set up a Dispatcher to handle different endpoints
    Dispatcher dispatcher = new Dispatcher() {
      @NonNull
      @Override
      public MockResponse dispatch(RecordedRequest request) {
        String path = request.getPath();
        if (mock_post_req_url_validate_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockValidateAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_obtain_new_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockObtainNewAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_get_is_server_online_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockGetIsServerOnlineResponseJson(true)
            .toString());
        } else if (mock_post_req_url_search_user_app_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockSearchUserAppHasReturnResponseJson()
            .toString());

        }
        // Default response for any other requests
        return new MockResponse().setResponseCode(404);
      }
    };

    // Set the dispatcher on the server
    mockWebServer.setDispatcher(dispatcher);

    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent =
      TestUtils.createIntentWithSilentSignInMockData(mockWebServer);

    intent.putExtra(
      Constants.mockSearchUserAppUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_search_user_app_link)
        .toString()
    );

    ActivityScenario<MainActivity> scenario =
      ActivityScenario.launch(intent);

    // Input search text to search field
    onView(withId(R.id.search_field)).perform(
      typeText("eat"),
      closeSoftKeyboard()
    );

    // Mock search
    onView(withId(R.id.button_search)).perform(click());

    // Wait for the search operation to complete and verify results
    // The search operation is asynchronous, so we need to wait a bit
    try {
      Thread.sleep(3000); // Wait 3 seconds for the search to complete
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }

    // Verify that the RecyclerView is displayed and contains search results
    onView(withId(R.id.id_app_icon_text_recycler)).check(matches(isDisplayed()))
      .check(matches(hasMinimumChildCount(1))); // Should have at least 1 item from mock response

    // Clean up
    scenario.close();
    mockWebServer.shutdown();
  }

  @Test
  public void testSearchHasNoReturn()
    throws
    IOException {

    // Get method name for CodeRunManager
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Set up a Dispatcher to handle different endpoints
    Dispatcher dispatcher = new Dispatcher() {
      @NonNull
      @Override
      public MockResponse dispatch(RecordedRequest request) {
        String path = request.getPath();
        if (mock_post_req_url_validate_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockValidateAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_obtain_new_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockObtainNewAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_get_is_server_online_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockGetIsServerOnlineResponseJson(true)
            .toString());
        } else if (mock_post_req_url_search_user_app_link.equals(path)) {
          // Return a response with no search results (isSuccess = false)
          JSONObject noResultsResponse =
            TestUtils.createMockSearchUserAppResponse(
              false,
              // No successful results
              null,
              null
            );
          // Log the response to understand its structure
          android.util.Log.i(
            "TEST_DEBUG",
            "No results response: " + noResultsResponse.toString()
          );
          return new MockResponse().setBody(noResultsResponse.toString());

        }
        // Default response for any other requests
        return new MockResponse().setResponseCode(404);
      }
    };

    // Set the dispatcher on the server
    mockWebServer.setDispatcher(dispatcher);

    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent =
      TestUtils.createIntentWithSilentSignInMockData(mockWebServer);

    intent.putExtra(
      Constants.mockSearchUserAppUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_search_user_app_link)
        .toString()
    );

    ActivityScenario<MainActivity> scenario =
      ActivityScenario.launch(intent);

    // Input search text to search field
    onView(withId(R.id.search_field)).perform(
      typeText("nonexistentapp"),
      closeSoftKeyboard()
    );

    // Mock search
    onView(withId(R.id.button_search)).perform(click());

    // Get the initial child count before search (should be the scanned apps)
    final int[] initialChildCount = new int[1];
    onView(withId(R.id.id_app_icon_text_recycler)).check((view, noViewFoundException) -> {
      if (noViewFoundException != null) {
        throw noViewFoundException;
      }
      androidx.recyclerview.widget.RecyclerView recyclerView =
        (androidx.recyclerview.widget.RecyclerView) view;
      initialChildCount[0] = recyclerView.getChildCount();
      android.util.Log.i(
        "TEST_DEBUG",
        "Initial child count: " + initialChildCount[0]
      );
    });

    // Wait for the search operation to complete
    try {
      Thread.sleep(3000); // Wait 3 seconds for the search to complete
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }

    // Verify that the RecyclerView still shows the same apps (no search results were added)
    // When search returns no results, the RecyclerView should remain unchanged
    onView(withId(R.id.id_app_icon_text_recycler)).check((view, noViewFoundException) -> {
      if (noViewFoundException != null) {
        throw noViewFoundException;
      }
      androidx.recyclerview.widget.RecyclerView recyclerView =
        (androidx.recyclerview.widget.RecyclerView) view;
      int currentChildCount = recyclerView.getChildCount();
      android.util.Log.i(
        "TEST_DEBUG",
        "Current child count after search: " + currentChildCount
      );

      // The child count should remain the same since no search results were returned
      assertEquals("RecyclerView should have the same number of items when search returns no results",
        initialChildCount[0],
        currentChildCount
      );
    });

    // Clean up
    scenario.close();
    mockWebServer.shutdown();
  }

  @Test
  public void testSearchHasNoSearchText() {
  }
}
