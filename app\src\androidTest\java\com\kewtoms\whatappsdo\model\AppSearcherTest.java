package com.kewtoms.whatappsdo.model;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;
import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.hasMinimumChildCount;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_get_is_server_online_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_obtain_new_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_search_user_app_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_validate_access_token_link;
import static com.kewtoms.whatappsdo.data.TestUtilsConstants.CODE_RUN_FOR_TEST_FOLDER_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.kewtoms.whatappsdo.MainActivity;
import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScrapeData;
import com.kewtoms.whatappsdo.model.scrapeApps.AppScraper;
import com.kewtoms.whatappsdo.utils.CodeRunManager;
import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
import com.kewtoms.whatappsdo.utils.TestUtils;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;


@RunWith(AndroidJUnit4.class)

public class AppSearcherTest {

  @BeforeClass
  public static void disableAnimations() {
    // Disable all animation scales to ensure test stability
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global window_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global transition_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global animator_duration_scale 0");
  }

  @Test
  public void testSearchHasReturn()
    throws
    IOException {

    // Get method name for CodeRunManager
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Set up a Dispatcher to handle different endpoints
    Dispatcher dispatcher = new Dispatcher() {
      @NonNull
      @Override
      public MockResponse dispatch(RecordedRequest request) {
        String path = request.getPath();
        if (mock_post_req_url_validate_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockValidateAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_obtain_new_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockObtainNewAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_get_is_server_online_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockGetIsServerOnlineResponseJson(true)
            .toString());
        } else if (mock_post_req_url_search_user_app_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockSearchUserAppHasReturnResponseJson()
            .toString());

        }
        // Default response for any other requests
        return new MockResponse().setResponseCode(404);
      }
    };

    // Set the dispatcher on the server
    mockWebServer.setDispatcher(dispatcher);

    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent =
      TestUtils.createIntentWithSilentSignInMockData(mockWebServer);

    intent.putExtra(
      Constants.mockSearchUserAppUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_search_user_app_link)
        .toString()
    );

    ActivityScenario<MainActivity> scenario =
      ActivityScenario.launch(intent);

    // Input search text to search field
    onView(withId(R.id.search_field)).perform(
      typeText("eat"),
      closeSoftKeyboard()
    );

    // Mock search
    onView(withId(R.id.button_search)).perform(click());

  }

  @Test
  public void testSearchHasNoReturn() {
  }

  @Test
  public void testSearchHasNoSearchText() {
  }
}
