package com.kewtoms.whatappsdo.model;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;
import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.hasMinimumChildCount;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static org.hamcrest.Matchers.not;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_get_is_server_online_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_obtain_new_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_search_user_app_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_validate_access_token_link;
import static com.kewtoms.whatappsdo.data.TestUtilsConstants.CODE_RUN_FOR_TEST_FOLDER_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.kewtoms.whatappsdo.MainActivity;
import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScrapeData;
import com.kewtoms.whatappsdo.model.scrapeApps.AppScraper;
import com.kewtoms.whatappsdo.utils.CodeRunManager;
import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
import com.kewtoms.whatappsdo.utils.TestUtils;

import org.json.JSONObject;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;


@RunWith(AndroidJUnit4.class)

public class AppSearcherTest {

  @BeforeClass
  public static void disableAnimations() {
    // Disable all animation scales to ensure test stability
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global window_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global transition_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global animator_duration_scale 0");
  }

  @Test
  public void testSearchHasReturn()
    throws
    IOException {

    // Get method name for CodeRunManager
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Set up a Dispatcher to handle different endpoints
    Dispatcher dispatcher = new Dispatcher() {
      @NonNull
      @Override
      public MockResponse dispatch(RecordedRequest request) {
        String path = request.getPath();
        if (mock_post_req_url_validate_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockValidateAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_obtain_new_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockObtainNewAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_get_is_server_online_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockGetIsServerOnlineResponseJson(true)
            .toString());
        } else if (mock_post_req_url_search_user_app_link.equals(path)) {
          // Create a response with the correct structure that AppSearcher expects
          try {
            JSONObject searchResponse = new JSONObject();
            searchResponse.put("code", 200);
            searchResponse.put("is_success", true);
            searchResponse.put("message", "Search completed successfully");

            // Create Result object with search results (this is what AppSearcher expects)
            JSONObject resultObj = new JSONObject();
            JSONArray packageNames = new JSONArray();
            packageNames.put("com.whatsapp");
            packageNames.put("com.facebook.katana");
            packageNames.put("com.instagram.android");

            JSONArray scores = new JSONArray();
            scores.put(0.95);
            scores.put(0.87);
            scores.put(0.82);

            resultObj.put("packageNames", packageNames);
            resultObj.put("scores", scores);
            searchResponse.put("Result", resultObj);

            return new MockResponse().setBody(searchResponse.toString());
          } catch (JSONException e) {
            return new MockResponse().setResponseCode(500).setBody("Error creating mock response");
          }

        }
        // Default response for any other requests
        return new MockResponse().setResponseCode(404);
      }
    };

    // Set the dispatcher on the server
    mockWebServer.setDispatcher(dispatcher);

    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent =
      TestUtils.createIntentWithSilentSignInMockData(mockWebServer);

    intent.putExtra(
      Constants.mockSearchUserAppUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_search_user_app_link)
        .toString()
    );

    ActivityScenario<MainActivity> scenario =
      ActivityScenario.launch(intent);

    // Input search text to search field
    onView(withId(R.id.search_field)).perform(
      typeText("eat"),
      closeSoftKeyboard()
    );

    // Mock search
    onView(withId(R.id.button_search)).perform(click());

    // Wait for the search operation to complete and verify results
    // The search operation is asynchronous, so we need to wait a bit
    try {
      Thread.sleep(3000); // Wait 3 seconds for the search to complete
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }

    // Verify that the RecyclerView is displayed and contains search results
    onView(withId(R.id.id_app_icon_text_recycler)).check(matches(isDisplayed()))
      .check(matches(hasMinimumChildCount(1))); // Should have at least 1 item from mock response

    // Clean up
    scenario.close();
    mockWebServer.shutdown();
  }

  @Test
  public void testSearchHasNoReturn()
    throws
    IOException {

    // Get method name for CodeRunManager
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Set up a Dispatcher to handle different endpoints
    Dispatcher dispatcher = new Dispatcher() {
      @NonNull
      @Override
      public MockResponse dispatch(RecordedRequest request) {
        String path = request.getPath();
        if (mock_post_req_url_validate_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockValidateAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_obtain_new_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockObtainNewAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_get_is_server_online_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockGetIsServerOnlineResponseJson(true)
            .toString());
        } else if (mock_post_req_url_search_user_app_link.equals(path)) {
          // Create a response with the correct structure that AppSearcher expects
          // AppSearcher looks for "Result" key, not "data" key
          try {
            JSONObject noResultsResponse = new JSONObject();
            noResultsResponse.put("code", 200);
            noResultsResponse.put("is_success", true);
            noResultsResponse.put("message", "Search completed with no results");

            // Create empty Result object (this is what AppSearcher expects)
            JSONObject resultObj = new JSONObject();
            resultObj.put("packageNames", new JSONArray()); // Empty array
            resultObj.put("scores", new JSONArray()); // Empty array
            noResultsResponse.put("Result", resultObj);

            android.util.Log.i("TEST_DEBUG", "No results response: " + noResultsResponse.toString());
            return new MockResponse().setBody(noResultsResponse.toString());
          } catch (JSONException e) {
            return new MockResponse().setResponseCode(500).setBody("Error creating mock response");
          }

        }
        // Default response for any other requests
        return new MockResponse().setResponseCode(404);
      }
    };

    // Set the dispatcher on the server
    mockWebServer.setDispatcher(dispatcher);

    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent =
      TestUtils.createIntentWithSilentSignInMockData(mockWebServer);

    intent.putExtra(
      Constants.mockSearchUserAppUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_search_user_app_link)
        .toString()
    );

    ActivityScenario<MainActivity> scenario =
      ActivityScenario.launch(intent);

    // Input search text to search field
    onView(withId(R.id.search_field)).perform(
      typeText("nonexistentapp"),
      closeSoftKeyboard()
    );

    // Mock search
    onView(withId(R.id.button_search)).perform(click());

    // Wait for the search operation to complete
    try {
      Thread.sleep(3000); // Wait 3 seconds for the search to complete
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }

    // Verify that the RecyclerView is cleared when there are no search results
    // The RecyclerView should be empty after a search that returns no results
    onView(withId(R.id.id_app_icon_text_recycler))
      .check(matches(isDisplayed()))
      .check(matches(not(hasMinimumChildCount(1)))); // Should have no items after empty search

    // Clean up
    scenario.close();
    mockWebServer.shutdown();
  }

  @Test
  public void testSearchHasNoSearchText()
    throws
    IOException {

    // Get method name for CodeRunManager
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Set up a Dispatcher to handle different endpoints
    Dispatcher dispatcher = new Dispatcher() {
      @NonNull
      @Override
      public MockResponse dispatch(RecordedRequest request) {
        String path = request.getPath();
        if (mock_post_req_url_validate_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockValidateAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_obtain_new_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockObtainNewAccessTokenSuccessResponseJson()
            .toString());
        } else if (mock_post_req_url_get_is_server_online_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockGetIsServerOnlineResponseJson(true)
            .toString());
        } else if (mock_post_req_url_search_user_app_link.equals(path)) {
          // This should not be called since empty search text should not trigger a request
          return new MockResponse().setResponseCode(500).setBody("Unexpected request");
        }
        // Default response for any other requests
        return new MockResponse().setResponseCode(404);
      }
    };

    // Set the dispatcher on the server
    mockWebServer.setDispatcher(dispatcher);

    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent =
      TestUtils.createIntentWithSilentSignInMockData(mockWebServer);

    intent.putExtra(
      Constants.mockSearchUserAppUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_search_user_app_link)
        .toString()
    );

    ActivityScenario<MainActivity> scenario =
      ActivityScenario.launch(intent);

    // Leave search field empty (don't input any text)
    // Just close the keyboard if it's open
    onView(withId(R.id.search_field)).perform(closeSoftKeyboard());

    // Get the initial child count before search attempt
    final int[] initialChildCount = new int[1];
    onView(withId(R.id.id_app_icon_text_recycler)).check((view, noViewFoundException) -> {
      if (noViewFoundException != null) {
        throw noViewFoundException;
      }
      androidx.recyclerview.widget.RecyclerView recyclerView =
        (androidx.recyclerview.widget.RecyclerView) view;
      initialChildCount[0] = recyclerView.getChildCount();
    });

    // Attempt to search with empty text
    onView(withId(R.id.button_search)).perform(click());

    // Wait a bit to ensure no async operations are triggered
    try {
      Thread.sleep(1000); // Wait 1 second
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }

    // Verify that the RecyclerView remains unchanged when search text is empty
    // The AppSearcher should return early and not make any network requests
    onView(withId(R.id.id_app_icon_text_recycler)).check((view, noViewFoundException) -> {
      if (noViewFoundException != null) {
        throw noViewFoundException;
      }
      androidx.recyclerview.widget.RecyclerView recyclerView =
        (androidx.recyclerview.widget.RecyclerView) view;
      int currentChildCount = recyclerView.getChildCount();

      // The child count should remain the same since no search was performed
      assertEquals("RecyclerView should remain unchanged when search text is empty",
        initialChildCount[0], currentChildCount);
    });

    // Clean up
    scenario.close();
    mockWebServer.shutdown();
  }
}
