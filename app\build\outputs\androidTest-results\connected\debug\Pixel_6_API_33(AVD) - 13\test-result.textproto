# 2025-07-05T14:55:53.719507200Z:
test_suite_meta_data {
  scheduled_test_case_count: 1
}
test_status: FAILED
test_result {
  test_case {
    test_class: "AppSearcherTest"
    test_package: "com.kewtoms.whatappsdo.model"
    test_method: "testSearchHasNoReturn"
    start_time {
      seconds: 1751727352
      nanos: 872000000
    }
    end_time {
      seconds: 1751727352
      nanos: 911000000
    }
  }
  test_status: FAILED
  error {
    error_message: "java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties\nat com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)\nat com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)\n... 33 trimmed\nCaused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties\nat android.content.res.AssetManager.nativeOpenAsset(Native Method)\nat android.content.res.AssetManager.open(AssetManager.java:904)\nat android.content.res.AssetManager.open(AssetManager.java:881)\nat com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)\n... 35 more\n"
    error_type: "java.io.FileNotFoundException"
    stack_trace: "java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties\nat com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)\nat com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)\n... 33 trimmed\nCaused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties\nat android.content.res.AssetManager.nativeOpenAsset(Native Method)\nat android.content.res.AssetManager.open(AssetManager.java:904)\nat android.content.res.AssetManager.open(AssetManager.java:881)\nat com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)\n... 35 more\n"
  }
  output_artifact {
    label {
      label: "logcat"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\logcat-com.kewtoms.whatappsdo.model.AppSearcherTest-testSearchHasNoReturn.txt"
    }
  }
  output_artifact {
    label {
      label: "device-info"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\device-info.pb"
    }
  }
  output_artifact {
    label {
      label: "device-info.meminfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\meminfo"
    }
  }
  output_artifact {
    label {
      label: "device-info.cpuinfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\cpuinfo"
    }
  }
}
output_artifact {
  label {
    label: "test-results.log"
    namespace: "com.google.testing.platform.runtime.android.driver.AndroidInstrumentationDriver"
  }
  source_path {
    path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\testlog\\test-results.log"
  }
  type: TEST_DATA
  mime_type: "text/plain"
}
