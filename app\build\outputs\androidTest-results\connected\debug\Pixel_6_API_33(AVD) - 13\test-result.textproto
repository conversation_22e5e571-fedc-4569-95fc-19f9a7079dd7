# 2025-07-05T15:05:57.095224900Z:
test_suite_meta_data {
  scheduled_test_case_count: 1
}
test_status: PASSED
test_result {
  test_case {
    test_class: "AppSearcherTest"
    test_package: "com.kewtoms.whatappsdo.model"
    test_method: "testSearchHasNoReturn"
    start_time {
      seconds: 1751727946
      nanos: 899000000
    }
    end_time {
      seconds: 1751727956
      nanos: 68000000
    }
  }
  test_status: PASSED
  output_artifact {
    label {
      label: "logcat"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\logcat-com.kewtoms.whatappsdo.model.AppSearcherTest-testSearchHasNoReturn.txt"
    }
  }
  output_artifact {
    label {
      label: "device-info"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\device-info.pb"
    }
  }
  output_artifact {
    label {
      label: "device-info.meminfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\meminfo"
    }
  }
  output_artifact {
    label {
      label: "device-info.cpuinfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\cpuinfo"
    }
  }
}
output_artifact {
  label {
    label: "test-results.log"
    namespace: "com.google.testing.platform.runtime.android.driver.AndroidInstrumentationDriver"
  }
  source_path {
    path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\testlog\\test-results.log"
  }
  type: TEST_DATA
  mime_type: "text/plain"
}
