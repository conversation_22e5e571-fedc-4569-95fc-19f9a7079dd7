# 2025-07-05T15:25:49.679898300Z:
test_suite_meta_data {
  scheduled_test_case_count: 1
}
test_status: FAILED
test_result {
  test_case {
    test_class: "AppSearcherTest"
    test_package: "com.kewtoms.whatappsdo.model"
    test_method: "testSearchHasNoReturn"
    start_time {
      seconds: 1751729138
      nanos: 440000000
    }
    end_time {
      seconds: 1751729148
      nanos: 374000000
    }
  }
  test_status: FAILED
  error {
    error_message: "androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: \'(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\' doesn\'t match the selected view.\nExpected: (view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\nGot: view.getGlobalVisibleRect() returned empty rectangle\nView Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=0, height=0, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1878.0, child-count=0}\n\nat dalvik.system.VMStack.getThreadStackTrace(Native Method)\nat java.lang.Thread.getStackTrace(Thread.java:1841)\nat androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)\nat androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)\nat androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)\nat androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)\nat androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)\nat androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)\nat com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:304)\n... 33 trimmed\nCaused by: junit.framework.AssertionFailedError: \'(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\' doesn\'t match the selected view.\nExpected: (view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\nGot: view.getGlobalVisibleRect() returned empty rectangle\nView Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=0, height=0, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1878.0, child-count=0}\n\nat androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)\nat androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)\nat androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)\nat androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)\nat androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)\nat java.util.concurrent.FutureTask.run(FutureTask.java:264)\nat android.os.Handler.handleCallback(Handler.java:942)\nat android.os.Handler.dispatchMessage(Handler.java:99)\nat android.os.Looper.loopOnce(Looper.java:201)\nat android.os.Looper.loop(Looper.java:288)\nat android.app.ActivityThread.main(ActivityThread.java:7924)\nat java.lang.reflect.Method.invoke(Native Method)\nat com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)\nat com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)\n"
    error_type: "junit.framework.AssertionFailedError"
    stack_trace: "androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: \'(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\' doesn\'t match the selected view.\nExpected: (view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\nGot: view.getGlobalVisibleRect() returned empty rectangle\nView Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=0, height=0, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1878.0, child-count=0}\n\nat dalvik.system.VMStack.getThreadStackTrace(Native Method)\nat java.lang.Thread.getStackTrace(Thread.java:1841)\nat androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)\nat androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)\nat androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)\nat androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)\nat androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)\nat androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)\nat com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:304)\n... 33 trimmed\nCaused by: junit.framework.AssertionFailedError: \'(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\' doesn\'t match the selected view.\nExpected: (view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)\nGot: view.getGlobalVisibleRect() returned empty rectangle\nView Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=0, height=0, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1878.0, child-count=0}\n\nat androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)\nat androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)\nat androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)\nat androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)\nat androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)\nat java.util.concurrent.FutureTask.run(FutureTask.java:264)\nat android.os.Handler.handleCallback(Handler.java:942)\nat android.os.Handler.dispatchMessage(Handler.java:99)\nat android.os.Looper.loopOnce(Looper.java:201)\nat android.os.Looper.loop(Looper.java:288)\nat android.app.ActivityThread.main(ActivityThread.java:7924)\nat java.lang.reflect.Method.invoke(Native Method)\nat com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)\nat com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)\n"
  }
  output_artifact {
    label {
      label: "logcat"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\logcat-com.kewtoms.whatappsdo.model.AppSearcherTest-testSearchHasNoReturn.txt"
    }
  }
  output_artifact {
    label {
      label: "device-info"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\device-info.pb"
    }
  }
  output_artifact {
    label {
      label: "device-info.meminfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\meminfo"
    }
  }
  output_artifact {
    label {
      label: "device-info.cpuinfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\cpuinfo"
    }
  }
}
output_artifact {
  label {
    label: "test-results.log"
    namespace: "com.google.testing.platform.runtime.android.driver.AndroidInstrumentationDriver"
  }
  source_path {
    path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\testlog\\test-results.log"
  }
  type: TEST_DATA
  mime_type: "text/plain"
}
