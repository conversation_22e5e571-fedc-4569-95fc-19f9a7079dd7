1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.kewtoms.whatappsdo"
4    android:versionCode="404"
5    android:versionName="1.108.11" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
11-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:5:5-7:53
11-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:6:9-61
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:9:5-67
12-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:9:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:10:5-79
13-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:10:22-76
14    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
14-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\385e6bd4fe16a69db709dbdf59266a7a\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:9:5-98
14-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\385e6bd4fe16a69db709dbdf59266a7a\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:9:22-95
15
16    <permission
16-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb709be7328e58ead7a1634f73612ad4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.kewtoms.whatappsdo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb709be7328e58ead7a1634f73612ad4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb709be7328e58ead7a1634f73612ad4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.kewtoms.whatappsdo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb709be7328e58ead7a1634f73612ad4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb709be7328e58ead7a1634f73612ad4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
21    <uses-permission android:name="android.permission.REORDER_TASKS" />
21-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:5-72
21-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:22-69
22
23    <queries>
23-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1838109056e152418783e92f57d12f55\transformed\runner-1.5.0\AndroidManifest.xml:24:5-28:15
24        <package android:name="androidx.test.orchestrator" />
24-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1838109056e152418783e92f57d12f55\transformed\runner-1.5.0\AndroidManifest.xml:25:9-62
24-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1838109056e152418783e92f57d12f55\transformed\runner-1.5.0\AndroidManifest.xml:25:18-59
25        <package android:name="androidx.test.services" />
25-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1838109056e152418783e92f57d12f55\transformed\runner-1.5.0\AndroidManifest.xml:26:9-58
25-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1838109056e152418783e92f57d12f55\transformed\runner-1.5.0\AndroidManifest.xml:26:18-55
26        <package android:name="com.google.android.apps.common.testing.services" />
26-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1838109056e152418783e92f57d12f55\transformed\runner-1.5.0\AndroidManifest.xml:27:9-83
26-->[androidx.test:runner:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1838109056e152418783e92f57d12f55\transformed\runner-1.5.0\AndroidManifest.xml:27:18-80
27    </queries>
28
29    <application
29-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:13:5-34:19
30        android:allowBackup="true"
30-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:14:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb709be7328e58ead7a1634f73612ad4\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:15:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:16:9-54
36        android:icon="@mipmap/ic_launcher"
36-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:17:9-43
37        android:label="@string/app_name"
37-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:18:9-41
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:19:9-54
39        android:supportsRtl="true"
39-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:20:9-35
40        android:testOnly="true"
41        android:theme="@style/Theme.WhatAppsDo"
41-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:21:9-48
42        android:usesCleartextTraffic="true" >
42-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:22:9-44
43        <activity
43-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:24:9-33:20
44            android:name="com.kewtoms.whatappsdo.MainActivity"
44-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:25:13-41
45            android:exported="true"
45-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:26:13-36
46            android:theme="@style/Theme.WhatAppsDo.NoActionBar" >
46-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:27:13-64
47            <intent-filter>
47-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:28:13-32:29
48                <action android:name="android.intent.action.MAIN" />
48-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:29:17-69
48-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:29:25-66
49
50                <category android:name="android.intent.category.LAUNCHER" />
50-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:17-77
50-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:27-74
51            </intent-filter>
52        </activity>
53        <activity
53-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
54            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
54-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
55            android:excludeFromRecents="true"
55-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
56            android:exported="true"
56-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
57            android:launchMode="singleTask"
57-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
58            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
58-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
59            <intent-filter>
59-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
60                <action android:name="android.intent.action.VIEW" />
60-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
60-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
61
62                <category android:name="android.intent.category.DEFAULT" />
62-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
62-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
63                <category android:name="android.intent.category.BROWSABLE" />
63-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
63-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
64
65                <data
65-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
66                    android:host="firebase.auth"
66-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
67                    android:path="/"
67-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
68                    android:scheme="genericidp" />
68-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
69            </intent-filter>
70        </activity>
71        <activity
71-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
72            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
72-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
73            android:excludeFromRecents="true"
73-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
74            android:exported="true"
74-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
75            android:launchMode="singleTask"
75-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
76            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
76-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
77            <intent-filter>
77-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
78                <action android:name="android.intent.action.VIEW" />
78-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
78-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
79
80                <category android:name="android.intent.category.DEFAULT" />
80-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
80-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
81                <category android:name="android.intent.category.BROWSABLE" />
81-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
81-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
82
83                <data
83-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
84                    android:host="firebase.auth"
84-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
85                    android:path="/"
85-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
86                    android:scheme="recaptcha" />
86-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
87            </intent-filter>
88        </activity>
89
90        <service
90-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
91            android:name="com.google.firebase.components.ComponentDiscoveryService"
91-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:67:13-84
92            android:directBootAware="true"
92-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
93            android:exported="false" >
93-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:68:13-37
94            <meta-data
94-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
95                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
95-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3386ae77d40a4623e3aa02f3da3ed5d\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
97            <meta-data
97-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\560f5ab7d0ca3de56e786d2f76617463\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
98                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
98-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\560f5ab7d0ca3de56e786d2f76617463\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\560f5ab7d0ca3de56e786d2f76617463\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
100            <meta-data
100-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
101                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
101-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
103        </service>
104        <service
104-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
105            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
105-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
106            android:enabled="true"
106-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
107            android:exported="false" >
107-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
108            <meta-data
108-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
109                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
109-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
110                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
110-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
111        </service>
112
113        <activity
113-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
114            android:name="androidx.credentials.playservices.HiddenActivity"
114-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
115            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
115-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
116            android:enabled="true"
116-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
117            android:exported="false"
117-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
118            android:fitsSystemWindows="true"
118-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
119            android:theme="@style/Theme.Hidden" >
119-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c7ca7593398ae12ccedabecf0ed1d36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
120        </activity>
121        <activity
121-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
122            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
122-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
123            android:excludeFromRecents="true"
123-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
124            android:exported="false"
124-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
125            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
125-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
126        <!--
127            Service handling Google Sign-In user revocation. For apps that do not integrate with
128            Google Sign-In, this service will never be started.
129        -->
130        <service
130-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
131            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
131-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
132            android:exported="true"
132-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
133            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
133-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
134            android:visibleToInstantApps="true" />
134-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c32f21815a8c27fdfded6d4e6cf9b47b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
135
136        <provider
136-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
137            android:name="com.google.firebase.provider.FirebaseInitProvider"
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
138            android:authorities="com.kewtoms.whatappsdo.firebaseinitprovider"
138-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
139            android:directBootAware="true"
139-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
140            android:exported="false"
140-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
141            android:initOrder="100" />
141-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b21c672ade2369ca4e6eb7d3be80747\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
142
143        <activity
143-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\69c69650339d6ae92a9a16d57819dc96\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
144            android:name="com.google.android.gms.common.api.GoogleApiActivity"
144-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\69c69650339d6ae92a9a16d57819dc96\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
145            android:exported="false"
145-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\69c69650339d6ae92a9a16d57819dc96\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
146            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
146-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\69c69650339d6ae92a9a16d57819dc96\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
147
148        <provider
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fbf243a081ef122b8a06b96713394c0\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
149            android:name="androidx.startup.InitializationProvider"
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fbf243a081ef122b8a06b96713394c0\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
150            android:authorities="com.kewtoms.whatappsdo.androidx-startup"
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fbf243a081ef122b8a06b96713394c0\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
151            android:exported="false" >
151-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fbf243a081ef122b8a06b96713394c0\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
152            <meta-data
152-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fbf243a081ef122b8a06b96713394c0\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
153                android:name="androidx.emoji2.text.EmojiCompatInitializer"
153-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fbf243a081ef122b8a06b96713394c0\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
154                android:value="androidx.startup" />
154-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fbf243a081ef122b8a06b96713394c0\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
155            <meta-data
155-->[androidx.lifecycle:lifecycle-process:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ef942539da2d217690877e8622d923e\transformed\jetified-lifecycle-process-2.8.1\AndroidManifest.xml:29:13-31:52
156                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
156-->[androidx.lifecycle:lifecycle-process:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ef942539da2d217690877e8622d923e\transformed\jetified-lifecycle-process-2.8.1\AndroidManifest.xml:30:17-78
157                android:value="androidx.startup" />
157-->[androidx.lifecycle:lifecycle-process:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ef942539da2d217690877e8622d923e\transformed\jetified-lifecycle-process-2.8.1\AndroidManifest.xml:31:17-49
158            <meta-data
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
159                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
160                android:value="androidx.startup" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
161        </provider>
162
163        <uses-library
163-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc5cfe7d0471b036660ec6fe796721c3\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
164            android:name="androidx.window.extensions"
164-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc5cfe7d0471b036660ec6fe796721c3\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
165            android:required="false" />
165-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc5cfe7d0471b036660ec6fe796721c3\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
166        <uses-library
166-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc5cfe7d0471b036660ec6fe796721c3\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
167            android:name="androidx.window.sidecar"
167-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc5cfe7d0471b036660ec6fe796721c3\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
168            android:required="false" />
168-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc5cfe7d0471b036660ec6fe796721c3\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
169
170        <activity
170-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:27:9-34:20
171            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
171-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:28:13-99
172            android:exported="true"
172-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:29:13-36
173            android:theme="@style/WhiteBackgroundTheme" >
173-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:30:13-56
174            <intent-filter android:priority="-100" >
174-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
174-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
175                <category android:name="android.intent.category.LAUNCHER" />
175-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:17-77
175-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:27-74
176            </intent-filter>
177        </activity>
178        <activity
178-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:35:9-42:20
179            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
179-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:36:13-95
180            android:exported="true"
180-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:37:13-36
181            android:theme="@style/WhiteBackgroundTheme" >
181-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:38:13-56
182            <intent-filter android:priority="-100" >
182-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
182-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
183                <category android:name="android.intent.category.LAUNCHER" />
183-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:17-77
183-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:27-74
184            </intent-filter>
185        </activity>
186        <activity
186-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:43:9-50:20
187            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
187-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:44:13-103
188            android:exported="true"
188-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:45:13-36
189            android:theme="@style/WhiteBackgroundDialogTheme" >
189-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:46:13-62
190            <intent-filter android:priority="-100" >
190-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
190-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a80e7e5407ab1344959fc2659e0e678\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
191                <category android:name="android.intent.category.LAUNCHER" />
191-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:17-77
191-->D:\code\my_projects\WhatAppsDo\app\src\main\AndroidManifest.xml:31:27-74
192            </intent-filter>
193        </activity>
194
195        <meta-data
195-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\53efcbf5068393901ef746e1b6c051a1\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
196            android:name="com.google.android.gms.version"
196-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\53efcbf5068393901ef746e1b6c051a1\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
197            android:value="@integer/google_play_services_version" />
197-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\53efcbf5068393901ef746e1b6c051a1\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
198
199        <receiver
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
200            android:name="androidx.profileinstaller.ProfileInstallReceiver"
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
201            android:directBootAware="false"
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
202            android:enabled="true"
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
203            android:exported="true"
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
204            android:permission="android.permission.DUMP" >
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
206                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
207            </intent-filter>
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
209                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
212                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
215                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c75178b9d824d478cb5cf5dbc91212f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
216            </intent-filter>
217        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
218        <activity
218-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d21a9b012b7bf06bd892c5a8b8a9171\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
219            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
219-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d21a9b012b7bf06bd892c5a8b8a9171\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
220            android:exported="false"
220-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d21a9b012b7bf06bd892c5a8b8a9171\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
221            android:stateNotNeeded="true"
221-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d21a9b012b7bf06bd892c5a8b8a9171\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
222            android:theme="@style/Theme.PlayCore.Transparent" />
222-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\1d21a9b012b7bf06bd892c5a8b8a9171\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
223    </application>
224
225</manifest>
