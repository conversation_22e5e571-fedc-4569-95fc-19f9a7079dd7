EXECUTING: C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\aapt.exe dump badging D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\androidTest\debug\app-debug-androidTest.apk
CURRENT_WORKING_DIRECTORY: D:\code\my_projects\WhatAppsDo
START_TIME: 2025-07-05 23:30:05.436
START_TIME-NANOS: 2025-07-05 23:30:05.436369200
ENVIRONMENT:

*****************************************
STDOUT/STDERR BELOW
===================
package: name='com.kewtoms.whatappsdo.test' versionCode='' versionName='' platformBuildVersionName='14' platformBuildVersionCode='34' compileSdkVersion='34' compileSdkVersionCodename='14'
sdkVersion:'26'
targetSdkVersion:'34'
application: label='' icon=''
application-debuggable
uses-library:'android.test.runner'
feature-group: label=''
  uses-feature: name='android.hardware.faketouch'
  uses-implied-feature: name='android.hardware.faketouch' reason='default feature for all apps'
supports-screens: 'small' 'normal' 'large' 'xlarge'
supports-any-density: 'true'
locales:
densities:
===================
END_TIME: 2025-07-05 23:30:05.503
END_TIME-NANOS: 2025-07-05 23:30:05.503776700
DURATION: 67ms
EXIT CODE: 0
