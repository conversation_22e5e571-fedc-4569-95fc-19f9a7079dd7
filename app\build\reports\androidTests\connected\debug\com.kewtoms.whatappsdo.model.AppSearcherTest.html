<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>Test results - Class com.kewtoms.whatappsdo.model.AppSearcherTest</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.kewtoms.whatappsdo.model.AppSearcherTest</h1>
<div class="breadcrumbs">
<a href="index.html">all</a> &gt; 
<a href="com.kewtoms.whatappsdo.model.html">com.kewtoms.whatappsdo.model</a> &gt; AppSearcherTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">1</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">1</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="skipped">
<div class="counter">0</div>
<p>skipped</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">9.112s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="testSearchHasNoReturn"></a>
<h3 class="failures">testSearchHasNoReturn</h3>
<span class="code">
<pre>androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;
Got: was &lt;androidx.recyclerview.widget.RecyclerView{c06cd81 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}&gt;
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at dalvik.system.VMStack.getThreadStackTrace(Native Method)
at java.lang.Thread.getStackTrace(Thread.java:1841)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:262)
... 33 trimmed
Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;
Got: was &lt;androidx.recyclerview.widget.RecyclerView{c06cd81 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}&gt;
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Pixel_6_API_33(AVD) - 13</th>
</tr>
</thead>
<tr>
<td>testSearchHasNoReturn</td>
<td class="failures">failed (9.112s)</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>Generated by 
<a href="http://www.gradle.org">Gradle 8.9</a> at 2025年7月5日 22:58:15</p>
</div>
</div>
</body>