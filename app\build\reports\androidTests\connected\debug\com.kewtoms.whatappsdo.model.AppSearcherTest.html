<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>Test results - Class com.kewtoms.whatappsdo.model.AppSearcherTest</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.kewtoms.whatappsdo.model.AppSearcherTest</h1>
<div class="breadcrumbs">
<a href="index.html">all</a> &gt; 
<a href="com.kewtoms.whatappsdo.model.html">com.kewtoms.whatappsdo.model</a> &gt; AppSearcherTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">1</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">1</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="skipped">
<div class="counter">0</div>
<p>skipped</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.040s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="testSearchHasNoReturn"></a>
<h3 class="failures">testSearchHasNoReturn</h3>
<span class="code">
<pre>java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)
... 33 trimmed
Caused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties
at android.content.res.AssetManager.nativeOpenAsset(Native Method)
at android.content.res.AssetManager.open(AssetManager.java:904)
at android.content.res.AssetManager.open(AssetManager.java:881)
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)
... 35 more
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Pixel_6_API_33(AVD) - 13</th>
</tr>
</thead>
<tr>
<td>testSearchHasNoReturn</td>
<td class="failures">failed (0.040s)</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>Generated by 
<a href="http://www.gradle.org">Gradle 8.9</a> at 2025年7月5日 22:55:54</p>
</div>
</div>
</body>