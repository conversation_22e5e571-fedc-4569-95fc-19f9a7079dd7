<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>Test results - Class com.kewtoms.whatappsdo.model.AppSearcherTest</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.kewtoms.whatappsdo.model.AppSearcherTest</h1>
<div class="breadcrumbs">
<a href="index.html">all</a> &gt; 
<a href="com.kewtoms.whatappsdo.model.html">com.kewtoms.whatappsdo.model</a> &gt; AppSearcherTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">1</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">1</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="skipped">
<div class="counter">0</div>
<p>skipped</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">5.902s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="testSearchHasReturn"></a>
<h3 class="failures">testSearchHasReturn</h3>
<span class="code">
<pre>androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 540, 1099 and precision: 16, 16' on view 'view.getId() is &lt;2131230836/com.kewtoms.whatappsdo:id/button_search&gt;'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:179)
... 33 trimmed
Caused by: java.lang.ArrayIndexOutOfBoundsException: length=22; index=-1
at java.util.ArrayList.get(ArrayList.java:439)
at com.kewtoms.whatappsdo.model.AppSearcher.AppSearcher$1.onPostSuccess(AppSearcher.java:197)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:667)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:592)
at com.android.volley.toolbox.JsonRequest.deliverResponse(JsonRequest.java:100)
at com.android.volley.ExecutorDelivery$ResponseDeliveryRunnable.run(ExecutorDelivery.java:102)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Pixel_6_API_33(AVD) - 13</th>
</tr>
</thead>
<tr>
<td>testSearchHasReturn</td>
<td class="failures">failed (5.902s)</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>Generated by 
<a href="http://www.gradle.org">Gradle 8.9</a> at 2025年7月5日 23:30:06</p>
</div>
</div>
</body>