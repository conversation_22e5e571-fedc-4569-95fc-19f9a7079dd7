import org.gradle.api.tasks.JavaExec

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.google.gms.google.services)
}

android {
    namespace = "com.kewtoms.whatappsdo"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.kewtoms.whatappsdo"
        minSdk = 26
        targetSdk = 34
        versionCode = 405
        versionName = "1.108.12"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding = true
        compose = true
        buildConfig = true
    }

    // Add the generated source folder to the main source set.
    sourceSets {
        getByName("main") {
            java.srcDir("build/generated/source/enums")
        }
    }
}


val generateEnum by tasks.registering {
    // Adjust the path to your enum.txt file as needed.
    val inputFile = file("src/main/assets/enum.txt")
    // We place the generated source in a package-specific subfolder.
    // For example, if your package is "com.example.generated":
    val packagePath = "com/example/generated"
    val outputDir = file("$buildDir/generated/source/enums/$packagePath")

    // Mark the file as an input and the directory as an output (for incremental builds)
    inputs.file(inputFile)
    outputs.dir(outputDir)

    doLast {
        // Ensure our output directory exists.
        outputDir.mkdirs()

        // Read enumerator names (filter out blank lines)
        val enumValues = inputFile.readLines().map { it.trim() }.filter { it.isNotEmpty() }

        // Define the package and enum class name.
        val packageName = "com.example.generated" // change as needed
        val enumName = "MyEnum"                  // change to your desired enum name

        // Build up the enum class source code.
        val enumSource = buildString {
            appendLine("package $packageName;")
            appendLine()
            appendLine("public enum $enumName {")
            enumValues.forEachIndexed { index, value ->
                append("    ${value.uppercase()}")
                if (index != enumValues.lastIndex) {
                    appendLine(",")
                } else {
                    appendLine(";")
                }
            }
            appendLine("}")
        }

        // Write the file
        val outputFile = File(outputDir, "$enumName.java")
        outputFile.writeText(enumSource)
        println("Generated enum class at: ${outputFile.absolutePath}")
    }
}

// Ensure the enum generation runs before any Java compilation. In Android projects,
// you may have several JavaCompile tasks (for different build variants). One way is:
tasks.whenTaskAdded {
    if (name.startsWith("compile") && name.endsWith("JavaWithJavac")) {
        dependsOn(generateEnum)
    }
}

dependencies {

    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.constraintlayout)
    implementation(libs.lifecycle.livedata.ktx)
    implementation(libs.lifecycle.viewmodel.ktx)
    implementation(libs.navigation.fragment)
    implementation("com.android.volley:volley:1.2.1")
    implementation("org.jsoup:jsoup:1.17.2")
    implementation("com.google.code.gson:gson:2.11.0")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.10.3")
    implementation("com.fasterxml.jackson.core:jackson-core:2.10.3")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.10.3")
    implementation("com.macasaet.fernet:fernet-java8:1.4.2")
    implementation("org.json:json:20240303")
    implementation("com.squareup.okhttp3:mockwebserver:4.12.0")
    implementation(libs.navigation.ui)
    implementation(libs.firebase.auth)

    implementation("androidx.test.ext:junit:1.1.5")
    implementation("androidx.security:security-crypto:1.1.0-alpha03")
    implementation("androidx.test.espresso:espresso-core:3.5.0")
    implementation("androidx.test:rules:1.5.0")
    implementation("androidx.test:runner:1.5.0")
    implementation(files("libs\\utils-1.39.0.jar"))
}
