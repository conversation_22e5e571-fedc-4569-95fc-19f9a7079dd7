INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.model.AppSearcherTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stream=
com.kewtoms.whatappsdo.model.AppSearcherTest:
INSTRUMENTATION_STATUS: test=testSearchHasNoReturn
INSTRUMENTATION_STATUS_CODE: 1
INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.model.AppSearcherTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stack=java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)
... 33 trimmed
Caused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties
at android.content.res.AssetManager.nativeOpenAsset(Native Method)
at android.content.res.AssetManager.open(AssetManager.java:904)
at android.content.res.AssetManager.open(AssetManager.java:881)
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)
... 35 more

INSTRUMENTATION_STATUS: stream=
Error in testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest):
java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)
... 33 trimmed
Caused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties
at android.content.res.AssetManager.nativeOpenAsset(Native Method)
at android.content.res.AssetManager.open(AssetManager.java:904)
at android.content.res.AssetManager.open(AssetManager.java:881)
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)
... 35 more

INSTRUMENTATION_STATUS: test=testSearchHasNoReturn
INSTRUMENTATION_STATUS_CODE: -2
INSTRUMENTATION_RESULT: stream=

Time: 0.125
There was 1 failure:
1) testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)
... 33 trimmed
Caused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties
at android.content.res.AssetManager.nativeOpenAsset(Native Method)
at android.content.res.AssetManager.open(AssetManager.java:904)
at android.content.res.AssetManager.open(AssetManager.java:881)
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)
... 35 more

FAILURES!!!
Tests run: 1,  Failures: 1


INSTRUMENTATION_CODE: -1

