INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.model.AppSearcherTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stream=
com.kewtoms.whatappsdo.model.AppSearcherTest:
INSTRUMENTATION_STATUS: test=testSearchHasReturn
INSTRUMENTATION_STATUS_CODE: 1
s_glBindAttribLocation: bind attrib 0 name position
s_glBindAttribLocation: bind attrib 1 name color
s_glBindAttribLocation: bind attrib 2 name localCoord
INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.model.AppSearcherTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stack=androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 540, 1099 and precision: 16, 16' on view 'view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:179)
... 33 trimmed
Caused by: java.lang.ArrayIndexOutOfBoundsException: length=22; index=-1
at java.util.ArrayList.get(ArrayList.java:439)
at com.kewtoms.whatappsdo.model.AppSearcher.AppSearcher$1.onPostSuccess(AppSearcher.java:197)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:667)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:592)
at com.android.volley.toolbox.JsonRequest.deliverResponse(JsonRequest.java:100)
at com.android.volley.ExecutorDelivery$ResponseDeliveryRunnable.run(ExecutorDelivery.java:102)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

INSTRUMENTATION_STATUS: stream=
Error in testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest):
androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 540, 1099 and precision: 16, 16' on view 'view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:179)
... 33 trimmed
Caused by: java.lang.ArrayIndexOutOfBoundsException: length=22; index=-1
at java.util.ArrayList.get(ArrayList.java:439)
at com.kewtoms.whatappsdo.model.AppSearcher.AppSearcher$1.onPostSuccess(AppSearcher.java:197)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:667)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:592)
at com.android.volley.toolbox.JsonRequest.deliverResponse(JsonRequest.java:100)
at com.android.volley.ExecutorDelivery$ResponseDeliveryRunnable.run(ExecutorDelivery.java:102)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

INSTRUMENTATION_STATUS: test=testSearchHasReturn
INSTRUMENTATION_STATUS_CODE: -2
INSTRUMENTATION_RESULT: stream=

Time: 6.315
There was 1 failure:
1) testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 540, 1099 and precision: 16, 16' on view 'view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:179)
... 33 trimmed
Caused by: java.lang.ArrayIndexOutOfBoundsException: length=22; index=-1
at java.util.ArrayList.get(ArrayList.java:439)
at com.kewtoms.whatappsdo.model.AppSearcher.AppSearcher$1.onPostSuccess(AppSearcher.java:197)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:667)
at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:592)
at com.android.volley.toolbox.JsonRequest.deliverResponse(JsonRequest.java:100)
at com.android.volley.ExecutorDelivery$ResponseDeliveryRunnable.run(ExecutorDelivery.java:102)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

FAILURES!!!
Tests run: 1,  Failures: 1


INSTRUMENTATION_CODE: -1

