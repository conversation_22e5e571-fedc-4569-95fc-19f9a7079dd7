INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.model.AppSearcherTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stream=
com.kewtoms.whatappsdo.model.AppSearcherTest:
INSTRUMENTATION_STATUS: test=testSearchHasNoReturn
INSTRUMENTATION_STATUS_CODE: 1
s_glBindAttribLocation: bind attrib 0 name position
s_glBindAttribLocation: bind attrib 1 name color
s_glBindAttribLocation: bind attrib 2 name localCoord
INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.model.AppSearcherTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stack=androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at dalvik.system.VMStack.getThreadStackTrace(Native Method)
at java.lang.Thread.getStackTrace(Thread.java:1841)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:271)
... 33 trimmed
Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

INSTRUMENTATION_STATUS: stream=
Error in testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest):
androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at dalvik.system.VMStack.getThreadStackTrace(Native Method)
at java.lang.Thread.getStackTrace(Thread.java:1841)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:271)
... 33 trimmed
Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

INSTRUMENTATION_STATUS: test=testSearchHasNoReturn
INSTRUMENTATION_STATUS_CODE: -2
INSTRUMENTATION_RESULT: stream=

Time: 10.173
There was 1 failure:
1) testSearchHasNoReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at dalvik.system.VMStack.getThreadStackTrace(Native Method)
at java.lang.Thread.getStackTrace(Thread.java:1841)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:271)
... 33 trimmed
Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

FAILURES!!!
Tests run: 1,  Failures: 1


INSTRUMENTATION_CODE: -1

