07-05 14:48:27.697 11153 11175 I TestRunner: started: testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 14:48:27.698  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:48:27.698  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:48:27.699  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:48:27.700  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:48:27.703  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:48:27.707  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:48:27.715  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:48:27.720   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:48:27.721   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:48:27.722   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:48:27.731 11153 11175 D APP:SecurePrefsManager: saveSignInData: run
07-05 14:48:27.732 11153 11175 D APP:SecurePrefsManager: saveAccessToken: run
07-05 14:48:27.732 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:27.757 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:87)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:48:27.978 11153 11175 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:87)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:48:28.049 11153 11175 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:48:28.082 11153 11175 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 14:48:28.093 11153 11175 D APP:SecurePrefsManager: saveAccessToken: done
07-05 14:48:28.093 11153 11175 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 14:48:28.093 11153 11175 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 14:48:28.093 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:28.096 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:28.126 11153 11175 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 14:48:28.126 11153 11175 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 14:48:28.126 11153 11175 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 14:48:28.126 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:28.128 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:28.161 11153 11175 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 14:48:28.161 11153 11175 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 14:48:28.161 11153 11175 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 14:48:28.161 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:28.162 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:28.191 11153 11175 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 14:48:28.192 11153 11175 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 14:48:28.192 11153 11175 D APP:SecurePrefsManager: saveLoginTime: run
07-05 14:48:28.192 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:28.193 11153 11175 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:28.230 11153 11175 D APP:SecurePrefsManager: saveLoginTime: done
07-05 14:48:28.230 11153 11175 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 14:48:28.230 11153 11175 D APP:SecurePrefsManager: saveSignInData: done
07-05 14:48:28.425   336 11202 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:28.426 11153 11175 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 14:48:28.444   336 11204 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:28.463 11153 11175 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 14:48:28.496   796   930 D SplashScreenView: Build android.window.SplashScreenView{cf69d8b V.E...... ......ID 0,0-0,0}
07-05 14:48:28.496   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 14:48:28.496   796   930 D SplashScreenView: Branding: view: android.view.View{d839e68 G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 14:48:28.501   796   966 W Parcel  : Expecting binder but got null!
07-05 14:48:28.505  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 14:48:28.507 11153 11206 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 14:48:28.509 11153 11206 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 14:48:28.513 11153 11206 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 14:48:28.540  1166  1782 D EGL_emulation: app_time_stats: avg=2208.41ms min=589.31ms max=3827.50ms count=2
07-05 14:48:28.577   564   594 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 14:48:28.577   564   594 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 14:48:28.577   564   594 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 14:48:28.577   564   594 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 14:48:28.577   564   594 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 14:48:28.577   564   594 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 14:48:28.577   564   594 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 14:48:28.577   564   594 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 14:48:28.585   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:48:28.672 11153 11153 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 14:48:28.682 11153 11153 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 14:48:28.683 11153 11153 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 14:48:28.717   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d53d0
07-05 14:48:28.718   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3ab0
07-05 14:48:28.718   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3ab0
07-05 14:48:28.718   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d53d0
07-05 14:48:28.780   796   966 D EGL_emulation: app_time_stats: avg=27090.67ms min=27090.67ms max=27090.67ms count=1
07-05 14:48:28.814 11153 11153 D APP:MainActivity: onCreate: run
07-05 14:48:28.815 11153 11153 D APP:Constants: initializeData: run
07-05 14:48:28.815 11153 11153 D APP:Constants: initializeData: done
07-05 14:48:28.836   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3330
07-05 14:48:29.120   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2cd0
07-05 14:48:29.382 11153 11153 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10200; state: ENABLED
07-05 14:48:29.420 11153 11153 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 14:48:29.420 11153 11153 I APP:MainActivity: onCreate: done setting root view
07-05 14:48:29.445 11153 11153 I APP:MainActivity: onCreate: Done initializing drawer
07-05 14:48:29.517 11153 11153 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 14:48:30.333 11153 11153 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 231.450ms (1201.12 bytecodes/s) (8240B approximate peak alloc)
07-05 14:48:30.369 11153 11153 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 14:48:30.369 11153 11153 D APP:MainActivity: onCreate: Done
07-05 14:48:30.371 11153 11153 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 14:48:30.372 11153 11153 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:48:30.376 11153 11153 D APP:HomeFragment: onCreateView: run
07-05 14:48:30.462 11153 11153 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 14:48:30.462 11153 11153 I CodeRunManager: Feature HomeFragment.runThreadCheckAndCachePackagesRelated not found in config. Using default value: true
07-05 14:48:30.463 11153 11153 I APP:HomeFragment: silentSignIn: run
07-05 14:48:30.466 11153 11209 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: Cache directories created successfully
07-05 14:48:30.649 11153 11209 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 131.529ms (243.29 bytecodes/s) (2528B approximate peak alloc)
07-05 14:48:30.941 11153 11209 I APP:HomeFragment: sending get request to get_is_server_online_link: http://localhost:8000/__mock_get_is_server_online
07-05 14:48:30.979 11153 11153 D APP:Authenticator: silentSignIn: run
07-05 14:48:30.979 11153 11153 D APP:Authenticator: hasAccountStored: run
07-05 14:48:30.979 11153 11153 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:48:30.980 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:30.985 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.012   336 11210 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:31.013 11153 11209 D TrafficStats: tagSocket(103) with statsTag=0xffffffff, statsUid=-1
07-05 14:48:31.017 11153 11203 D TrafficStats: tagSocket(105) with statsTag=0xffffffff, statsUid=-1
07-05 14:48:31.033 11153 11153 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:48:31.034 11153 11153 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:48:31.034 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.037 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.062   336 11214 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:31.089 11153 11153 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:48:31.090 11153 11153 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:48:31.090 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.094 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.135 11153 11153 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:48:31.135 11153 11153 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 14:48:31.135 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.138 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.189 11153 11153 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 14:48:31.190 11153 11153 D APP:Authenticator: hasAccountStored: end: true
07-05 14:48:31.191 11153 11153 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:48:31.192 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.195 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.234 11153 11153 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:48:31.234 11153 11153 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:48:31.235 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.235 11153 11215 I APP:ScanAppCallable: run: pool-3-thread-1
07-05 14:48:31.237 11153 11215 I System.out: Directories created successfully
07-05 14:48:31.238 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.275 11153 11215 I APP:ScanAppCallable: converting com.android.camera2 icon drawable to bitmap
07-05 14:48:31.276 11153 11153 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:48:31.276 11153 11153 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:48:31.276 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.280 11153 11153 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.287 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.303 11153 11215 I APP:ScanAppCallable: converting com.android.chrome icon drawable to bitmap
07-05 14:48:31.313 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.318 11153 11153 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:48:31.329 11153 11215 I APP:ScanAppCallable: converting com.android.settings icon drawable to bitmap
07-05 14:48:31.337 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.351 11153 11215 I APP:ScanAppCallable: converting com.google.android.apps.docs icon drawable to bitmap
07-05 14:48:31.357 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.372 11153 11153 D APP:Authenticator: validateAccessToken: run
07-05 14:48:31.372 11153 11153 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 14:48:31.373 11153 11153 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:48:31.376 11153 11153 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:48:31.377 11153 11216 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:48:31.378 11153 11215 I APP:ScanAppCallable: converting com.google.android.apps.maps icon drawable to bitmap
07-05 14:48:31.378 11153 11216 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:48:31.393 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.413 11153 11215 I APP:ScanAppCallable: converting com.google.android.apps.messaging icon drawable to bitmap
07-05 14:48:31.429 11153 11216 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:48:31.430 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.431 11153 11153 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:48:31.431 11153 11153 D APP:Authenticator: validateAccessToken: end
07-05 14:48:31.431 11153 11153 D APP:Authenticator: silentSignIn: done
07-05 14:48:31.433 11153 11153 D APP:HomeFragment: silentSignIn: done
07-05 14:48:31.438 11153 11153 D APP:HomeFragment: onCreateView: done
07-05 14:48:31.440 11153 11219 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:48:31.440 11153 11219 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.445 11153 11219 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.452 11153 11215 I APP:ScanAppCallable: converting com.google.android.apps.photos icon drawable to bitmap
07-05 14:48:31.455 11153 11153 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 14:48:31.456 11153 11153 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:48:31.461 11153 11153 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 14:48:31.461 11153 11153 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:48:31.466 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.470 11153 11153 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10200; state: DISABLED
07-05 14:48:31.477 11153 11205 W Parcel  : Expecting binder but got null!
07-05 14:48:31.483 11153 11215 I APP:ScanAppCallable: converting com.google.android.apps.youtube.music icon drawable to bitmap
07-05 14:48:31.491 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.499 11153 11219 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:48:31.509   336 11224 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:31.512 11153 11215 I APP:ScanAppCallable: converting com.google.android.calendar icon drawable to bitmap
07-05 14:48:31.526 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.541 11153 11215 I APP:ScanAppCallable: converting com.google.android.contacts icon drawable to bitmap
07-05 14:48:31.547   398   545 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10200 pid=0
07-05 14:48:31.547   398  1303 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10200 pid=11153
07-05 14:48:31.547   398   545 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10200 => denied (569 us)
07-05 14:48:31.547   398  1303 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10200 => denied (527 us)
07-05 14:48:31.548   398  1303 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10200 pid=11153
07-05 14:48:31.548   398  1303 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10200 => denied (237 us)
07-05 14:48:31.548   398   545 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10200 pid=0
07-05 14:48:31.548   398   545 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10200 => denied (350 us)
07-05 14:48:31.548   398  1303 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10200 pid=11153
07-05 14:48:31.548   398  1303 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10200 => denied (352 us)
07-05 14:48:31.549   398   545 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10200 pid=0
07-05 14:48:31.549   398   545 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10200 => denied (798 us)
07-05 14:48:31.549 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.560 11153 11205 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:48:31.561 11153 11153 E RecyclerView: No adapter attached; skipping layout
07-05 14:48:31.563 11153 11205 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 14:48:31.563 11153 11205 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 14:48:31.579 11153 11215 I APP:ScanAppCallable: converting com.google.android.deskclock icon drawable to bitmap
07-05 14:48:31.580 11153 11205 D EGL_emulation: eglCreateContext: 0x720c5f5e1e90: maj 3 min 1 rcv 4
07-05 14:48:31.615 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.619   796   966 D EGL_emulation: app_time_stats: avg=26595.35ms min=26595.35ms max=26595.35ms count=1
07-05 14:48:31.623 11153 11205 D EGL_emulation: eglMakeCurrent: 0x720c5f5e1e90: ver 3 1 (tinfo 0x720e7729f080) (first time)
07-05 14:48:31.631 11153 11215 I APP:ScanAppCallable: converting com.google.android.dialer icon drawable to bitmap
07-05 14:48:31.673   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 14:48:31.673 11153 11205 I Gralloc4: mapper 4.x is not supported
07-05 14:48:31.678 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.678   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 14:48:31.680   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 14:48:31.680 11153 11205 W Gralloc4: allocator 4.x is not supported
07-05 14:48:31.693 11153 11215 I APP:ScanAppCallable: converting com.google.android.gm icon drawable to bitmap
07-05 14:48:31.700 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.703 11153 11205 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:48:31.703 11153 11205 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:48:31.718 11153 11215 I APP:ScanAppCallable: converting com.google.android.youtube icon drawable to bitmap
07-05 14:48:31.730 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.749 11153 11215 I APP:ScanAppCallable: converting com.android.stk icon drawable to bitmap
07-05 14:48:31.755 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.769 11153 11215 I APP:ScanAppCallable: converting com.google.android.documentsui icon drawable to bitmap
07-05 14:48:31.781 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.793 11153 11215 I APP:ScanAppCallable: converting com.google.android.googlequicksearchbox icon drawable to bitmap
07-05 14:48:31.799 11153 11215 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:48:31.800   564   602 W ziparchive: Unable to open '/data/app/~~sL6XWMwTfraquq8-Zc7XMA==/com.kewtoms.whatappsdo-Q7JjpB0shgMFgzgxiQAwIQ==/base.dm': No such file or directory
07-05 14:48:31.801   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +3s328ms
07-05 14:48:31.814   564   594 W InputManager-JNI: Input channel object '61e6b09 Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 14:48:31.831  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 14:48:31.833  1166  2106 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef629ea0 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 14:48:31.837   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2fd0
07-05 14:48:31.846 11153 11153 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:48:31.847 11153 11153 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:48:31.848 11153 11153 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 14:48:31.849 11153 11153 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 14:48:31.849 11153 11153 I APP:Authenticator: silentSignIn: Validate success. Signing in..
07-05 14:48:31.849 11153 11153 D APP:Authenticator: signInUsingAccessToken:run
07-05 14:48:31.850 11153 11153 I APP:Authenticator: signInUsingAccessToken:: Sending login request:http://localhost:8000/__mock_sign_in_access_token
07-05 14:48:31.850 11153 11209 D APP:AppScraper: checkAppNeedScrape: disabled
07-05 14:48:31.850 11153 11209 D APP:AppScraper: scrapeWhenNeeded: disabled
07-05 14:48:31.851 11153 11209 D APP:AppScraper: sendScrapeData: disabled
07-05 14:48:31.851 11153 11209 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 14:48:31.852 11153 11153 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:48:31.854 11153 11153 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:48:31.857   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3bd0
07-05 14:48:31.862 11153 11227 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:48:31.863 11153 11227 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:48:31.865 11153 11227 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:48:31.866 11153 11153 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:48:31.866 11153 11153 D APP:Authenticator: signInUsingAccessToken:end
07-05 14:48:31.866 11153 11153 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 14:48:31.876 11153 11231 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:48:31.877 11153 11231 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:31.881 11153 11231 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:31.891  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 14:48:31.892  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 14:48:31.893  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.kewtoms.whatappsdo fieldId=-1 fieldName=null extras=null}, false)
07-05 14:48:31.894  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 14:48:31.896  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:48:31.902   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4a70
07-05 14:48:31.926 11153 11231 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:48:31.933   336 11234 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:31.936 11153 11231 E Volley  : [84] NetworkUtility.shouldRetryException: Unexpected response code 404 for http://localhost:8000/__mock_sign_in_access_token
07-05 14:48:31.942   796   966 D EGL_emulation: app_time_stats: avg=3162.11ms min=3162.11ms max=3162.11ms count=1
07-05 14:48:31.964 11153 11153 D InsetsController: show(ime(), fromIme=false)
07-05 14:48:31.970 11153 11153 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10200; state: ENABLED
07-05 14:48:31.970  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 14:48:31.970 11153 11153 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F....ID 265,536-815,683 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 14:48:31.971  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 14:48:31.976  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 14:48:31.976  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 14:48:31.979  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:48:31.979 11153 11153 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F....ID 265,536-815,683 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 14:48:31.981 11153 11153 W InputMethodManager: Ignoring onBind: cur seq=37, given seq=36
07-05 14:48:31.982 11153 11153 E APP:RequestUtils: getJsonObjectRequest.onErrorResponse: VolleyError: com.android.volley.ClientError
07-05 14:48:31.982 11153 11153 I APP:Authenticator: signInUsingAccessToken:: Login using access token failed
07-05 14:48:31.983  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:31.987  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 14:48:31.989  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:31.989  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:48:31.989 11153 11153 I AssistStructure: Flattened final assist data: 2432 bytes, containing 1 windows, 15 views
07-05 14:48:31.991  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 14:48:31.993  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 14:48:31.994  1371  1371 I AndroidIME: AbstractIme.onActivate():86 LatinIme.onActivate() : EditorInfo = inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 14:48:31.996  1371  1371 I Delight5Facilitator: Delight5Facilitator.initializeForIme():777 initializeForIme() : Locale = [en_US], layout = qwerty
07-05 14:48:31.997  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.cancelShutdown():55 cancelShutdown()
07-05 14:48:31.998  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.syncLanguagePacks():67 syncLanguagePacks()
07-05 14:48:32.001  1371 10352 I SpeechFactory: SpeechRecognitionFactory.maybeScheduleAutoPackDownloadForFallback():205 maybeScheduleAutoPackDownloadForFallback()
07-05 14:48:32.001  1371 10352 I FallbackOnDeviceRecognitionProvider: FallbackOnDeviceRecognitionProvider.maybeScheduleAutoPackDownload():195 maybeScheduleAutoPackDownload() for language tag en-US
07-05 14:48:32.002  1371  1371 I LatinIme: LatinIme.updateEnableInlineSuggestionsOnDecoderSideFlags():1003 inline flag updated to:false
07-05 14:48:32.010  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 14:48:32.019  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 14:48:32.029  1371  1380 I putmethod.latin: JIT allocated 52KB for compiled code of mzo hod.d(java.util.List)
07-05 14:48:32.038  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:48:32.038  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.039  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_OPTIONAL
07-05 14:48:32.039  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:48:32.039  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 14:48:32.039  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:48:32.040  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 14:48:32.040  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 14:48:32.042  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:48:32.045  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.047  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.048  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.048  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 14:48:32.049  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:48:32.049  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 14:48:32.049  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:48:32.050  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 14:48:32.051  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 14:48:32.052  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:321642244
07-05 14:48:32.055  1371  1371 I ProactiveSuggestionsHolderManager: ProactiveSuggestionsHolderManager$3.display():216 Requesting to show proactive suggestions: CLIPBOARD PREEMPTIVE_WITH_SUPPRESSION
07-05 14:48:32.057  1274  2762 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef616250 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 14:48:32.059  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 14:48:32.072  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.073  1274  2760 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:48:32.074  1274  2760 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:48:32.080  1274  2760 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:48:32.098  1274  2762 I FontLog : Pulling font file for id = 46, cache size = 9 [CONTEXT service_id=132 ]
07-05 14:48:32.106  1274  2762 I FontLog : Pulling font file for id = 46, cache size = 9 [CONTEXT service_id=132 ]
07-05 14:48:32.159  1371  1939 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:48:32.170 11153 11175 W FileTestStorage: Output properties is not supported.
07-05 14:48:32.175 11153 11175 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 14:48:32.203  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.205 11153 11153 D InsetsController: show(ime(), fromIme=true)
07-05 14:48:32.206  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.208  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:48:32.214  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 14:48:32.228  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 14:48:32.246  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:48:32.246  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:48:32.250  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:48:32.258  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:48:32.258  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:48:32.263  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:48:32.286 11153 11175 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 14:48:32.286 11153 11175 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 14:48:32.286 11153 11175 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 14:48:32.286 11153 11175 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 14:48:32.362 11153 11175 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 14:48:32.395 11153 11153 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:48:32.396 11153 11153 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:48:32.397 11153 11153 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 14:48:32.413 11153 11153 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 14:48:32.414 11153 11153 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:48:32.414 11153 11153 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:48:32.435 11153 11153 I ViewInteraction: Performing 'type text(eat)' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 14:48:32.445  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7303442, downTime=7303442, deviceId=-1, source=0x1002, displayId=0, eventId=-299215455 }
07-05 14:48:32.484  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7303482, downTime=7303442, deviceId=-1, source=0x1002, displayId=0, eventId=-17996942 }
07-05 14:48:32.486 11153 11153 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F.P..ID 265,126-815,273 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
07-05 14:48:32.491  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:48:32.499 11153 11153 D InsetsController: show(ime(), fromIme=true)
07-05 14:48:32.719 11153 11153 D UiControllerImpl: Injecting string: "eat"
07-05 14:48:32.726   564   943 D InputDispatcher: Touch mode switch rejected, caller (pid=0, uid=10200) doesn't own the focused window nor none of the previously interacted window
07-05 14:48:32.751  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:48:32.751  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:48:32.759  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:48:32.789  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 14:48:32.790  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:48:32.791  1371  1371 I KeyboardViewController: KeyboardViewController.lambda$showSelfAndAncestors$6():609 current view doesn't has the priority com.google.android.apps.inputmethod.latin.keyboard.widget.LatinFixedCountCandidatesHolderView{63d1438 I.E...... ......ID 0,0-850,116 #7f0b141a app:id/softkey_holder_fixed_candidates} to show itself, PREEMPTIVE
07-05 14:48:32.818 11153 11153 I ViewInteraction: Performing 'close keyboard' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 14:48:32.819 11153 11153 D IdlingRegistry: Registering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@d60238c]
07-05 14:48:32.822  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 14:48:32.822  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 14:48:32.830  1371  1371 I InputBundle: InputBundle.consumeEvent():923 Skip consuming an event as keyboard status is 0
07-05 14:48:32.831  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 14:48:32.832  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.shutdown():77 shutdown()
07-05 14:48:32.834  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 LatinIme.onDeactivate()
07-05 14:48:32.838  1371  1872 E native  : E0000 00:00:1751726912.838673    1872 keyboard.cc:27] Cannot create a keyboard with 0 valid keys
07-05 14:48:32.845  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 29<30, inputStateId=16, lastInputStateId=17
07-05 14:48:32.928 11153 11240 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 14:48:33.070   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4410
07-05 14:48:33.164 11153 11153 D IdlingRegistry: Unregistering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@d60238c]
07-05 14:48:33.167 11153 11153 I ViewInteraction: Performing 'single click' action on view view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>
07-05 14:48:33.171  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7304168, downTime=7304168, deviceId=-1, source=0x1002, displayId=0, eventId=-704356251 }
07-05 14:48:33.213  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=7304211, downTime=7304168, deviceId=-1, source=0x1002, displayId=0, eventId=-1019835880 }
07-05 14:48:33.224 11153 11153 I APP:HomeFragment: onCreateView: searchButton onClick
07-05 14:48:33.226 11153 11153 I APP:HomeFragment: onCreateView: userUsageCount: 0
07-05 14:48:33.229 11153 11153 I APP:AppSearcher: Searching for app: eat
07-05 14:48:33.230 11153 11153 I APP:AppSearcher: jsonBody:{"data_str":"{\"allowSearch\":true,\"appIds\":[\"com.android.camera2\",\"com.android.chrome\",\"com.android.settings\",\"com.google.android.apps.docs\",\"com.google.android.apps.maps\",\"com.google.android.apps.messaging\",\"com.google.android.apps.photos\",\"com.google.android.apps.youtube.music\",\"com.google.android.calendar\",\"com.google.android.contacts\",\"com.google.android.deskclock\",\"com.google.android.dialer\",\"com.google.android.gm\",\"com.google.android.youtube\",\"com.android.stk\",\"com.google.android.documentsui\",\"com.google.android.googlequicksearchbox\"],\"query\":\"eat\"}"}
07-05 14:48:33.230 11153 11153 I APP:AppSearcher: searchApp: Sending request to:http://localhost:8000/__mock_search_user_app
07-05 14:48:33.232 11153 11153 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:48:33.233 11153 11153 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:48:33.235 11153 11242 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:48:33.236 11153 11242 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:48:33.237   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(34)
07-05 14:48:33.241 11153 11242 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:48:33.241 11153 11245 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:48:33.242 11153 11245 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:48:33.243 11153 11153 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:48:33.245 11153 11245 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:48:33.246 11153 11153 I APP:HomeFragment: Hided soft keyboard
07-05 14:48:33.251 11153 11205 D EGL_emulation: app_time_stats: avg=110.93ms min=6.30ms max=389.04ms count=13
07-05 14:48:33.285 11153 11245 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:48:33.292   336 11250 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:33.292   336 11251 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10200 0}
07-05 14:48:33.304 11153 11153 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:48:33.304 11153 11153 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:48:33.304 11153 11153 I APP:AppSearcher: onPostSuccess: responseJsonObj:{"code":200,"is_success":true,"message":"Operation successful.","data":{"packageNames":"[com.whatsapp, com.facebook.katana, com.instagram.android]","scores":"[0.95, 0.87, 0.82]"}}
07-05 14:48:33.307 11153 11153 D CompatibilityChangeReporter: Compat change id reported: 147798919; UID 10200; state: ENABLED
07-05 14:48:33.328   796   966 W Parcel  : Expecting binder but got null!
07-05 14:48:34.595   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:48:34.602 11153 11205 D EGL_emulation: app_time_stats: avg=270.08ms min=10.79ms max=1260.07ms count=5
07-05 14:48:34.637   796   796 I Choreographer: Skipped 77 frames!  The application may be doing too much work on its main thread.
07-05 14:48:34.645   796  1251 I OpenGLRenderer: Davey! duration=1285ms; Flags=1, FrameTimelineVsyncId=3025313, IntendedVsync=7304334832552, Vsync=7304334832552, InputEventId=0, HandleInputStart=7304336783700, AnimationStart=7304336813900, PerformTraversalsStart=7304336872900, DrawStart=7305595569200, FrameDeadline=7304351499218, FrameInterval=7304336771900, FrameStartTime=16666666, SyncQueued=7305598186500, SyncStart=7305614451800, IssueDrawCommandsStart=7305615020900, SwapBuffers=7305615948900, FrameCompleted=7305636210100, DequeueBufferDuration=17300, QueueBufferDuration=168800, GpuCompleted=7305636210100, SwapBuffersCompleted=7305633364300, DisplayPresentTime=-5304301426330789521, CommandSubmissionCompleted=7305615948900,
07-05 14:48:34.645 11153 11167 I OpenGLRenderer: Davey! duration=770ms; Flags=0, FrameTimelineVsyncId=3025378, IntendedVsync=7304834832532, Vsync=7304834832532, InputEventId=0, HandleInputStart=7304836921300, AnimationStart=7304836939100, PerformTraversalsStart=7304836967500, DrawStart=7304837253300, FrameDeadline=7304851499198, FrameInterval=7304836902100, FrameStartTime=16666666, SyncQueued=7304837515400, SyncStart=7304837720700, IssueDrawCommandsStart=7304837815100, SwapBuffers=7304838841700, FrameCompleted=7305605272400, DequeueBufferDuration=147700, QueueBufferDuration=340900, GpuCompleted=7305605272400, SwapBuffersCompleted=7305602529200, DisplayPresentTime=0, CommandSubmissionCompleted=7304838841700,
07-05 14:48:35.856 11153 11205 D EGL_emulation: app_time_stats: avg=310.22ms min=30.61ms max=500.48ms count=4
07-05 14:48:36.478 11153 11153 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)}' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 14:48:36.485 11153 11153 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>}' assertion on view view.getId() is <2131230971/com.kewtoms.whatappsdo:id/id_app_icon_text_recycler>
07-05 14:48:36.501 11153 11153 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PAUSED
07-05 14:48:36.501 11153 11153 V ActivityScenario: Update currentActivityStage to PAUSED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:48:36.516 11153 11153 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f in: PRE_ON_CREATE
07-05 14:48:36.520 11153 11153 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f
07-05 14:48:36.533 11153 11153 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f in: CREATED
07-05 14:48:36.533 11153 11153 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f
07-05 14:48:36.536 11153 11153 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f in: STARTED
07-05 14:48:36.536 11153 11153 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f
07-05 14:48:36.540 11153 11153 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f in: RESUMED
07-05 14:48:36.540 11153 11153 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f
07-05 14:48:36.544 11153 11205 W Parcel  : Expecting binder but got null!
07-05 14:48:36.548 11153 11162 W toms.whatappsdo: Cleared Reference was only reachable from finalizer (only reported once)
07-05 14:48:36.569 11153 11164 W System  : A resource failed to call close.
07-05 14:48:36.570 11153 11164 W System  : A resource failed to call close.
07-05 14:48:36.570 11153 11164 W System  : A resource failed to call close.
07-05 14:48:36.595 11153 11205 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:48:36.667   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity: +174ms
07-05 14:48:36.707 11153 11153 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STOPPED
07-05 14:48:36.707 11153 11153 V ActivityScenario: Update currentActivityStage to STOPPED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:48:36.711 11153 11153 D AutofillManager: onActivityFinishing(): calling cancelLocked()
07-05 14:48:36.725 11153 11153 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: DESTROYED
07-05 14:48:36.726 11153 11153 V ActivityScenario: Update currentActivityStage to DESTROYED, currentActivity=null
07-05 14:48:36.731   564   943 W InputManager-JNI: Input channel object '50a3d9c com.kewtoms.whatappsdo/com.kewtoms.whatappsdo.MainActivity (client)' was disposed without first being removed with the input manager!
07-05 14:48:36.736   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d35d0
07-05 14:48:36.736   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4d10
07-05 14:48:36.737   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d48f0
07-05 14:48:36.737   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4d10
07-05 14:48:36.773  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 14:48:36.775  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 14:48:36.776  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.kewtoms.whatappsdo fieldId=-1 fieldName=null extras=null}, false)
07-05 14:48:36.776  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 14:48:36.778  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:48:36.782 11153 11153 D LifecycleMonitor: Lifecycle status change: androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f in: PAUSED
07-05 14:48:36.783 11153 11153 V ActivityScenario: Activity lifecycle changed event received but ignored because the intent does not match. startActivityIntent=Intent { flg=0x10008000 cmp=com.kewtoms.whatappsdo/.MainActivity (has extras) }, activity.getIntent()=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.kewtoms.whatappsdo/androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity }, activity=androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity@117fa9f
07-05 14:48:36.792   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3030
07-05 14:48:36.793   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d17d0
07-05 14:48:36.797  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 14:48:36.806   564  1694 W AppSearchIcing: icing-search-engine.cc:217: Error: 5, Message: Document (com.google.android.googlequicksearchbox$OneSearchZeroStateGoogleSuggestions/default, zp) not found.
07-05 14:48:36.810  1166  1910 D OneSearchSuggestProvider: Created the binder channel successfully for end point service =com.google.android.apps.search.googleapp.search.suggest.plugins.onesearch.server.OneSearchSuggestService , mChannel=Q0{delegate=L0{logId=65, target=directaddress:///AndroidComponentAddress%5BIntent%20%7B%20act=grpc.io.action.BIND%20cmp=com.google.android.googlequicksearchbox/com.google.android.apps.search.googleapp.search.suggest.plugins.onesearch.server.OneSearchSuggestService%20%7D%5D}} , mOneSearchConnection=y1.J@da16a3a
07-05 14:48:36.815 11153 11175 I TestRunner: finished: testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
