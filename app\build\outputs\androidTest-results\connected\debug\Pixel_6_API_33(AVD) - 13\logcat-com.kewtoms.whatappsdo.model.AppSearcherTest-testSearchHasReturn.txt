07-05 15:29:57.294 14522 14544 I TestRunner: started: testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 15:29:57.301  1543  1736 I AiAiEcho: Predicting[0]:
07-05 15:29:57.301  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 15:29:57.309  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:29:57.313  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:29:57.322  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:29:57.326  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:29:57.331  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:29:57.332 14522 14544 D APP:SecurePrefsManager: saveSignInData: run
07-05 15:29:57.334 14522 14544 D APP:SecurePrefsManager: saveAccessToken: run
07-05 15:29:57.335 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:29:57.342  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:29:57.346  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:29:57.347  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:29:57.350   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:29:57.350  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:29:57.354   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:29:57.355   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:29:57.372 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:93)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:29:57.611 14522 14544 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:93)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:29:57.687 14522 14544 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:29:57.716 14522 14544 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 15:29:57.731 14522 14544 D APP:SecurePrefsManager: saveAccessToken: done
07-05 15:29:57.732 14522 14544 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 15:29:57.733 14522 14544 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 15:29:57.733 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:29:57.736 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:29:57.770 14522 14544 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 15:29:57.770 14522 14544 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 15:29:57.770 14522 14544 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 15:29:57.770 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:29:57.773 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:29:57.806 14522 14544 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 15:29:57.806 14522 14544 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 15:29:57.807 14522 14544 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 15:29:57.807 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:29:57.809 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:29:57.846 14522 14544 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 15:29:57.846 14522 14544 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 15:29:57.846 14522 14544 D APP:SecurePrefsManager: saveLoginTime: run
07-05 15:29:57.846 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:29:57.850 14522 14544 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:29:57.887 14522 14544 D APP:SecurePrefsManager: saveLoginTime: done
07-05 15:29:57.887 14522 14544 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 15:29:57.887 14522 14544 D APP:SecurePrefsManager: saveSignInData: done
07-05 15:29:58.124   336 14571 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10218 0}
07-05 15:29:58.126 14522 14544 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 15:29:58.149   336 14573 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10218 0}
07-05 15:29:58.171 14522 14544 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 15:29:58.200   796   930 D SplashScreenView: Build android.window.SplashScreenView{ff7aee7 V.E...... ......ID 0,0-0,0}
07-05 15:29:58.200   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 15:29:58.200   796   930 D SplashScreenView: Branding: view: android.view.View{16a0a94 G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 15:29:58.203   796   966 W Parcel  : Expecting binder but got null!
07-05 15:29:58.206  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 15:29:58.211 14522 14575 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 15:29:58.213 14522 14575 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 15:29:58.217 14522 14575 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 15:29:58.227   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:29:58.231   564  2245 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 15:29:58.231   564  2245 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 15:29:58.231   564  2245 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 15:29:58.231   564  2245 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 15:29:58.231   564  2245 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 15:29:58.231   564  2245 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 15:29:58.231   564  2245 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 15:29:58.231   564  2245 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 15:29:58.238   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:29:58.263   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:29:58.269  1166  1782 D EGL_emulation: app_time_stats: avg=2105.62ms min=337.77ms max=3873.47ms count=2
07-05 15:29:58.370   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2cd0
07-05 15:29:58.370   398   429 W TransactionTracing: Could not find layer handle 0x71102f7cf730
07-05 15:29:58.370   398   429 W TransactionTracing: Could not find layer handle 0x71102f7cf730
07-05 15:29:58.370   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2cd0
07-05 15:29:58.394   796   807 I ndroid.systemui: Background concurrent copying GC freed 330250(17MB) AllocSpace objects, 0(0B) LOS objects, 49% free, 16MB/33MB, paused 1.886ms,958us total 136.805ms
07-05 15:29:58.435   796   966 D EGL_emulation: app_time_stats: avg=42977.93ms min=42977.93ms max=42977.93ms count=1
07-05 15:29:58.463 14522 14522 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 15:29:58.473 14522 14522 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 15:29:58.473 14522 14522 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 15:29:58.502   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:29:58.503   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:29:58.519   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d1d10
07-05 15:29:58.572 14522 14522 D APP:MainActivity: onCreate: run
07-05 15:29:58.574 14522 14522 D APP:Constants: initializeData: run
07-05 15:29:58.575 14522 14522 D APP:Constants: initializeData: done
07-05 15:29:59.199 14522 14522 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10218; state: ENABLED
07-05 15:29:59.234 14522 14522 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 15:29:59.235 14522 14522 I APP:MainActivity: onCreate: done setting root view
07-05 15:29:59.259 14522 14522 I APP:MainActivity: onCreate: Done initializing drawer
07-05 15:29:59.322 14522 14522 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 15:29:59.843 14522 14522 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 250.885ms (1108.07 bytecodes/s) (8240B approximate peak alloc)
07-05 15:29:59.879 14522 14522 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 15:29:59.880 14522 14522 D APP:MainActivity: onCreate: Done
07-05 15:29:59.880 14522 14522 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 15:29:59.881 14522 14522 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:29:59.885 14522 14522 D APP:HomeFragment: onCreateView: run
07-05 15:29:59.975 14522 14522 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 15:29:59.975 14522 14522 I CodeRunManager: Feature HomeFragment.runThreadCheckAndCachePackagesRelated not found in config. Using default value: true
07-05 15:29:59.975 14522 14522 I APP:HomeFragment: silentSignIn: run
07-05 15:29:59.976 14522 14578 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: Cache directories created successfully
07-05 15:30:00.030  1543  1736 I AiAiEcho: Predicting[0]:
07-05 15:30:00.030  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 15:30:00.031   796   966 D EGL_emulation: app_time_stats: avg=23190.48ms min=317.28ms max=46063.68ms count=2
07-05 15:30:00.031  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 15:30:00.033  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 15:30:00.046  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 15:30:00.055   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 15:30:00.205 14522 14578 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 157.897ms (202.66 bytecodes/s) (2528B approximate peak alloc)
07-05 15:30:00.401 14522 14578 I APP:HomeFragment: sending get request to get_is_server_online_link: http://localhost:8000/__mock_get_is_server_online
07-05 15:30:00.429 14522 14522 D APP:Authenticator: silentSignIn: run
07-05 15:30:00.429 14522 14522 D APP:Authenticator: hasAccountStored: run
07-05 15:30:00.429 14522 14522 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:30:00.430 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.435 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.470   336 14579 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10218 0}
07-05 15:30:00.471 14522 14578 D TrafficStats: tagSocket(103) with statsTag=0xffffffff, statsUid=-1
07-05 15:30:00.476 14522 14572 D TrafficStats: tagSocket(105) with statsTag=0xffffffff, statsUid=-1
07-05 15:30:00.495 14522 14522 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:30:00.495 14522 14522 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:30:00.495 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.499 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.525   336 14583 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10218 0}
07-05 15:30:00.552 14522 14522 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:30:00.552 14522 14522 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:30:00.553 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.556 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.595 14522 14522 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:30:00.595 14522 14522 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 15:30:00.595 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.597 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.655 14522 14522 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 15:30:00.657 14522 14522 D APP:Authenticator: hasAccountStored: end: true
07-05 15:30:00.657 14522 14522 D APP:SecurePrefsManager: getAccountEmail: run
07-05 15:30:00.657 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.661 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.703 14522 14522 D APP:SecurePrefsManager: getAccountEmail: done
07-05 15:30:00.703 14522 14522 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:30:00.703 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.706 14522 14584 I APP:ScanAppCallable: run: pool-3-thread-1
07-05 15:30:00.707 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.709 14522 14584 I System.out: Directories created successfully
07-05 15:30:00.746 14522 14522 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:30:00.747 14522 14522 D APP:SecurePrefsManager: getRefreshToken: run
07-05 15:30:00.747 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.750 14522 14522 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.751 14522 14584 I APP:ScanAppCallable: converting com.android.camera2 icon drawable to bitmap
07-05 15:30:00.759 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.774 14522 14584 I APP:ScanAppCallable: converting com.android.chrome icon drawable to bitmap
07-05 15:30:00.785 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.785 14522 14522 D APP:SecurePrefsManager: getRefreshToken: done
07-05 15:30:00.798 14522 14584 I APP:ScanAppCallable: converting com.android.settings icon drawable to bitmap
07-05 15:30:00.809 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.823 14522 14584 I APP:ScanAppCallable: converting com.google.android.apps.docs icon drawable to bitmap
07-05 15:30:00.828 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.837 14522 14522 D APP:Authenticator: validateAccessToken: run
07-05 15:30:00.837 14522 14522 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 15:30:00.838 14522 14522 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:30:00.841 14522 14522 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:30:00.845 14522 14585 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:30:00.845 14522 14585 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:30:00.849 14522 14584 I APP:ScanAppCallable: converting com.google.android.apps.maps icon drawable to bitmap
07-05 15:30:00.861 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.877 14522 14584 I APP:ScanAppCallable: converting com.google.android.apps.messaging icon drawable to bitmap
07-05 15:30:00.889 14522 14585 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:30:00.890 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.891 14522 14522 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:30:00.891 14522 14522 D APP:Authenticator: validateAccessToken: end
07-05 15:30:00.893 14522 14522 D APP:Authenticator: silentSignIn: done
07-05 15:30:00.893 14522 14522 D APP:HomeFragment: silentSignIn: done
07-05 15:30:00.894 14522 14588 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:30:00.895 14522 14588 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:00.896 14522 14522 D APP:HomeFragment: onCreateView: done
07-05 15:30:00.901 14522 14588 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:00.906 14522 14584 I APP:ScanAppCallable: converting com.google.android.apps.photos icon drawable to bitmap
07-05 15:30:00.907 14522 14522 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 15:30:00.908 14522 14522 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:30:00.914 14522 14522 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 15:30:00.914 14522 14522 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 15:30:00.919 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.923 14522 14522 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10218; state: DISABLED
07-05 15:30:00.930 14522 14574 W Parcel  : Expecting binder but got null!
07-05 15:30:00.937 14522 14584 I APP:ScanAppCallable: converting com.google.android.apps.youtube.music icon drawable to bitmap
07-05 15:30:00.943 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.954 14522 14588 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:30:00.959   336 14593 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10218 0}
07-05 15:30:00.962 14522 14584 I APP:ScanAppCallable: converting com.google.android.calendar icon drawable to bitmap
07-05 15:30:00.976 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:00.991 14522 14584 I APP:ScanAppCallable: converting com.google.android.contacts icon drawable to bitmap
07-05 15:30:00.993   398  1303 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10218 pid=0
07-05 15:30:00.993   398   428 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10218 pid=14522
07-05 15:30:00.993   398  1303 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10218 => denied (1357 us)
07-05 15:30:00.994   398   428 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10218 => denied (867 us)
07-05 15:30:00.994   398   428 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10218 pid=14522
07-05 15:30:00.994   398  1303 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10218 pid=0
07-05 15:30:00.994   398   428 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10218 => denied (356 us)
07-05 15:30:00.994   398  1303 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10218 => denied (444 us)
07-05 15:30:00.994   398   428 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10218 pid=14522
07-05 15:30:00.994   398   428 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10218 => denied (245 us)
07-05 15:30:00.995   398  1303 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10218 pid=0
07-05 15:30:00.996   398  1303 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10218 => denied (1522 us)
07-05 15:30:00.996 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.005 14522 14574 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:30:01.013 14522 14522 E RecyclerView: No adapter attached; skipping layout
07-05 15:30:01.017 14522 14574 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 15:30:01.017 14522 14574 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 15:30:01.020   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2f70
07-05 15:30:01.020   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4b30
07-05 15:30:01.031 14522 14584 I APP:ScanAppCallable: converting com.google.android.deskclock icon drawable to bitmap
07-05 15:30:01.037 14522 14574 D EGL_emulation: eglCreateContext: 0x720c5f5e4f50: maj 3 min 1 rcv 4
07-05 15:30:01.048   796   966 D EGL_emulation: app_time_stats: avg=1016.63ms min=1016.63ms max=1016.63ms count=1
07-05 15:30:01.062 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.079 14522 14584 I APP:ScanAppCallable: converting com.google.android.dialer icon drawable to bitmap
07-05 15:30:01.084 14522 14574 D EGL_emulation: eglMakeCurrent: 0x720c5f5e4f50: ver 3 1 (tinfo 0x720e77346080) (first time)
07-05 15:30:01.113   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 15:30:01.113 14522 14574 I Gralloc4: mapper 4.x is not supported
07-05 15:30:01.116 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.117   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 15:30:01.117   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 15:30:01.118 14522 14574 W Gralloc4: allocator 4.x is not supported
07-05 15:30:01.130 14522 14584 I APP:ScanAppCallable: converting com.google.android.gm icon drawable to bitmap
07-05 15:30:01.136 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.142 14522 14574 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:30:01.143 14522 14574 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:30:01.154 14522 14584 I APP:ScanAppCallable: converting com.google.android.youtube icon drawable to bitmap
07-05 15:30:01.165 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.179 14522 14584 I APP:ScanAppCallable: converting com.android.stk icon drawable to bitmap
07-05 15:30:01.184 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.209 14522 14584 I APP:ScanAppCallable: converting com.google.android.documentsui icon drawable to bitmap
07-05 15:30:01.224 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.237 14522 14584 I APP:ScanAppCallable: converting com.google.android.googlequicksearchbox icon drawable to bitmap
07-05 15:30:01.245 14522 14584 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 15:30:01.257   564   602 W ziparchive: Unable to open '/data/app/~~0cSn-8E40fL-M0GgqSVSYA==/com.kewtoms.whatappsdo-HBJcroEmcL9hXkk0-CfiTw==/base.dm': No such file or directory
07-05 15:30:01.259   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +3s75ms
07-05 15:30:01.265 14522 14578 D APP:AppScraper: checkAppNeedScrape: disabled
07-05 15:30:01.266 14522 14578 D APP:AppScraper: scrapeWhenNeeded: disabled
07-05 15:30:01.266 14522 14578 D APP:AppScraper: sendScrapeData: disabled
07-05 15:30:01.266 14522 14578 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 15:30:01.287   564  1119 W InputManager-JNI: Input channel object '19da14c Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 15:30:01.297  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 15:30:01.298  1166  1185 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef630680 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 15:30:01.299 14522 14522 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:30:01.299 14522 14522 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:30:01.300 14522 14522 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 15:30:01.313 14522 14522 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 15:30:01.314 14522 14522 I APP:Authenticator: silentSignIn: Validate success. Signing in..
07-05 15:30:01.315 14522 14522 D APP:Authenticator: signInUsingAccessToken:run
07-05 15:30:01.316 14522 14522 I APP:Authenticator: signInUsingAccessToken:: Sending login request:http://localhost:8000/__mock_sign_in_access_token
07-05 15:30:01.318 14522 14522 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:30:01.318 14522 14522 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:30:01.324 14522 14596 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:30:01.324 14522 14596 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:30:01.328 14522 14596 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:30:01.328   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5d30
07-05 15:30:01.329   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d41d0
07-05 15:30:01.330 14522 14522 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:30:01.330 14522 14522 D APP:Authenticator: signInUsingAccessToken:end
07-05 15:30:01.331 14522 14522 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 15:30:01.338 14522 14599 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:30:01.339 14522 14599 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:01.341 14522 14599 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:01.354   796   966 D EGL_emulation: app_time_stats: avg=2918.95ms min=2918.95ms max=2918.95ms count=1
07-05 15:30:01.367 14522 14522 D InsetsController: show(ime(), fromIme=false)
07-05 15:30:01.383  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.383  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.386  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null}, false)
07-05 15:30:01.386  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:30:01.387  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:30:01.389  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.389  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:30:01.390  1371  1371 I AndroidIME: AbstractIme.onActivate():86 PasswordIme.onActivate() : EditorInfo = inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:30:01.395  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:30:01.405  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:30:01.405  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY
07-05 15:30:01.406  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:30:01.406  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:30:01.406  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:30:01.406  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:30:01.407  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:30:01.407  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:30:01.408  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.410 14522 14599 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:30:01.411  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.411  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.411  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:30:01.411  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:30:01.412  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:30:01.412  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:30:01.413  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:30:01.414  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:30:01.417  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:30:01.417   336 14604 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10218 0}
07-05 15:30:01.421 14522 14599 E Volley  : [83] NetworkUtility.shouldRetryException: Unexpected response code 404 for http://localhost:8000/__mock_sign_in_access_token
07-05 15:30:01.424  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:30:01.424  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 15:30:01.454 14522 14522 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10218; state: ENABLED
07-05 15:30:01.459 14522 14522 I AssistStructure: Flattened final assist data: 2432 bytes, containing 1 windows, 15 views
07-05 15:30:01.463 14522 14522 E APP:RequestUtils: getJsonObjectRequest.onErrorResponse: VolleyError: com.android.volley.ClientError
07-05 15:30:01.463 14522 14522 I APP:Authenticator: signInUsingAccessToken:: Login using access token failed
07-05 15:30:01.473   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:30:01.483  1371  1939 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 15:30:01.504  1274  3332 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 15:30:01.504  1274  3332 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 15:30:01.513  1274  3332 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 15:30:01.534  1274  3332 I FontLog : Pulling font file for id = 57, cache size = 4 [CONTEXT service_id=132 ]
07-05 15:30:01.549  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:30:01.550  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:30:01.553  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 PasswordIme.onDeactivate()
07-05 15:30:01.555  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 15:30:01.559  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 15:30:01.565  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:30:01.565  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 15:30:01.566  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:30:01.566  1274  3332 I FontLog : Pulling font file for id = 57, cache size = 4 [CONTEXT service_id=132 ]
07-05 15:30:01.570 14522 14522 D InsetsController: show(ime(), fromIme=true)
07-05 15:30:01.570  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null}, false)
07-05 15:30:01.573  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 15:30:01.578  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 15:30:01.582  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 15:30:01.585  1371  1371 I AndroidIME: AbstractIme.onActivate():86 LatinIme.onActivate() : EditorInfo = inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=2131231154 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 15:30:01.586 14522 14522 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F...... 265,536-815,683 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 15:30:01.590  1371  1371 I Delight5Facilitator: Delight5Facilitator.initializeForIme():777 initializeForIme() : Locale = [en_US], layout = qwerty
07-05 15:30:01.594  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.cancelShutdown():55 cancelShutdown()
07-05 15:30:01.595  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.syncLanguagePacks():67 syncLanguagePacks()
07-05 15:30:01.599  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.601  1371  1371 I LatinIme: LatinIme.updateEnableInlineSuggestionsOnDecoderSideFlags():1003 inline flag updated to:false
07-05 15:30:01.604  1371 10352 I SpeechFactory: SpeechRecognitionFactory.maybeScheduleAutoPackDownloadForFallback():205 maybeScheduleAutoPackDownloadForFallback()
07-05 15:30:01.610  1371 10352 I FallbackOnDeviceRecognitionProvider: FallbackOnDeviceRecognitionProvider.maybeScheduleAutoPackDownload():195 maybeScheduleAutoPackDownload() for language tag en-US
07-05 15:30:01.613  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:30:01.619  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.629  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 15:30:01.639  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 15:30:01.645  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:30:01.647  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.654  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:30:01.654  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.654  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 15:30:01.654  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_OPTIONAL
07-05 15:30:01.655  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:30:01.657  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 15:30:01.657  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:30:01.657  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 15:30:01.658  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 15:30:01.660  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 15:30:01.661  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.663  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.664  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.664  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 15:30:01.664  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:30:01.666  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:30:01.669  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:30:01.670  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 15:30:01.671  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 15:30:01.686  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 15:30:01.687  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.689  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.691  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:811537776
07-05 15:30:01.698 14522 14544 W FileTestStorage: Output properties is not supported.
07-05 15:30:01.702 14522 14544 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 15:30:01.724  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.729  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.731 14522 14522 D InsetsController: show(ime(), fromIme=true)
07-05 15:30:01.739  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:01.740  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 15:30:01.741  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.741  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.741  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 15:30:01.744  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 15:30:01.746  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.758 14522 14544 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 15:30:01.758 14522 14544 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 15:30:01.758 14522 14544 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 15:30:01.758 14522 14544 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 15:30:01.769 14522 14522 D InsetsController: show(ime(), fromIme=true)
07-05 15:30:01.778  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.778  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.795  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.802   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:30:01.802   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:30:01.802   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:30:01.806  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.807  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.817  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:30:01.819  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.832  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.832  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.837  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 15:30:01.855 14522 14544 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 15:30:01.899 14522 14522 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:30:01.900 14522 14522 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 15:30:01.900 14522 14522 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 15:30:01.917 14522 14522 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 15:30:01.918 14522 14522 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:30:01.919 14522 14522 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 15:30:01.939 14522 14522 I ViewInteraction: Performing 'type text(eat)' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:30:01.951  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9792948, downTime=9792948, deviceId=-1, source=0x1002, displayId=0, eventId=-159925451 }
07-05 15:30:01.990  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=474.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9792989, downTime=9792948, deviceId=-1, source=0x1002, displayId=0, eventId=-293664798 }
07-05 15:30:01.992 14522 14522 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F.P..ID 265,126-815,273 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
07-05 15:30:01.993  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 15:30:02.005 14522 14522 D InsetsController: show(ime(), fromIme=true)
07-05 15:30:02.020   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:30:02.226 14522 14522 D UiControllerImpl: Injecting string: "eat"
07-05 15:30:02.231   564  2245 D InputDispatcher: Touch mode switch rejected, caller (pid=0, uid=10218) doesn't own the focused window nor none of the previously interacted window
07-05 15:30:02.269  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:30:02.269  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:30:02.303   398   429 W TransactionTracing: Could not find layer id -1
07-05 15:30:02.322  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY_WITH_ANIMATION
07-05 15:30:02.323  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 15:30:02.339 14522 14522 I ViewInteraction: Performing 'close keyboard' action on view view.getId() is <2131231154/com.kewtoms.whatappsdo:id/search_field>
07-05 15:30:02.344 14522 14522 D IdlingRegistry: Registering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@d60238c]
07-05 15:30:02.348  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 15:30:02.348  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 15:30:02.354  1371  1371 I InputBundle: InputBundle.consumeEvent():923 Skip consuming an event as keyboard status is 0
07-05 15:30:02.355  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 15:30:02.355  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.shutdown():77 shutdown()
07-05 15:30:02.356  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 LatinIme.onDeactivate()
07-05 15:30:02.365  1371  1872 E native  : E0000 00:00:1751729402.365130    1872 keyboard.cc:27] Cannot create a keyboard with 0 valid keys
07-05 15:30:02.373  1371  2057 I native  : I0000 00:00:1751729402.373853    2057 input-context-store.cc:255] Ignoring stale client request for FetchSuggestions
07-05 15:30:02.374  1371  2057 I native  : I0000 00:00:1751729402.374884    2057 input-context-store.cc:178] Ignoring stale client request for OverrideDecodedCandidates
07-05 15:30:02.380  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 202<203, inputStateId=0, lastInputStateId=183
07-05 15:30:02.394 14522 14574 D EGL_emulation: app_time_stats: avg=91.96ms min=13.15ms max=188.42ms count=12
07-05 15:30:02.506   796   966 D EGL_emulation: app_time_stats: avg=576.21ms min=572.50ms max=579.93ms count=2
07-05 15:30:02.586   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2130
07-05 15:30:02.597 14522 14609 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 15:30:02.694 14522 14522 D IdlingRegistry: Unregistering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@d60238c]
07-05 15:30:02.698 14522 14522 I ViewInteraction: Performing 'single click' action on view view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>
07-05 15:30:02.701  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9793699, downTime=9793699, deviceId=-1, source=0x1002, displayId=0, eventId=-763552317 }
07-05 15:30:02.743  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=9793742, downTime=9793699, deviceId=-1, source=0x1002, displayId=0, eventId=-436831879 }
07-05 15:30:02.757 14522 14522 I APP:HomeFragment: onCreateView: searchButton onClick
07-05 15:30:02.759 14522 14522 I APP:HomeFragment: onCreateView: userUsageCount: 0
07-05 15:30:02.761 14522 14522 I APP:AppSearcher: Searching for app: eat
07-05 15:30:02.762 14522 14522 I APP:AppSearcher: jsonBody:{"data_str":"{\"allowSearch\":true,\"appIds\":[\"com.android.camera2\",\"com.android.chrome\",\"com.android.settings\",\"com.google.android.apps.docs\",\"com.google.android.apps.maps\",\"com.google.android.apps.messaging\",\"com.google.android.apps.photos\",\"com.google.android.apps.youtube.music\",\"com.google.android.calendar\",\"com.google.android.contacts\",\"com.google.android.deskclock\",\"com.google.android.dialer\",\"com.google.android.gm\",\"com.google.android.youtube\",\"com.android.stk\",\"com.google.android.documentsui\",\"com.google.android.googlequicksearchbox\"],\"query\":\"eat\"}"}
07-05 15:30:02.763 14522 14522 I APP:AppSearcher: searchApp: Sending request to:http://localhost:8000/__mock_search_user_app
07-05 15:30:02.763 14522 14522 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 15:30:02.763   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(35)
07-05 15:30:02.765 14522 14522 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 15:30:02.767 14522 14612 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 15:30:02.769 14522 14612 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 15:30:02.773 14522 14612 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 15:30:02.774 14522 14522 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 15:30:02.776 14522 14615 D APP:SecurePrefsManager: getAccessToken: run
07-05 15:30:02.777 14522 14615 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 15:30:02.778 14522 14522 I APP:HomeFragment: Hided soft keyboard
07-05 15:30:02.782 14522 14615 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 15:30:02.824 14522 14615 D APP:SecurePrefsManager: getAccessToken: done
07-05 15:30:02.830   336 14619 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10218 0}
07-05 15:30:02.831   336 14620 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10218 0}
07-05 15:30:02.838 14522 14522 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 15:30:02.838 14522 14522 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 15:30:02.838 14522 14522 I APP:AppSearcher: onPostSuccess: responseJsonObj:{"code":200,"is_success":true,"message":"Search completed successfully","Result":{"packageNames":["com.whatsapp","com.facebook.katana","com.instagram.android"],"scores":[0.95,0.87,0.82]}}
07-05 15:30:02.865 14522 14522 D takeScreenshot: Found 1 global views to redraw
07-05 15:30:02.927   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 15:30:02.927 14508 14548 I Gralloc4: mapper 4.x is not supported
07-05 15:30:02.938 14508 14548 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:30:02.945 14508 14622 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 15:30:02.946 14508 14622 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 15:30:02.949 14508 14622 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 15:30:02.971 14508 14622 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 15:30:02.972 14508 14622 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 15:30:02.973 14508 14622 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 15:30:02.993 14508 14622 D EGL_emulation: eglCreateContext: 0x7c9b1b8e0190: maj 3 min 1 rcv 4
07-05 15:30:03.021 14508 14622 D EGL_emulation: eglMakeCurrent: 0x7c9b1b8e0190: ver 3 1 (tinfo 0x7c9d4c80d080) (first time)
07-05 15:30:03.070 14522 14522 W UiControllerImpl: ignoring signal of: DELAY_HAS_PAST from previous generation: 47 current generation: 48
07-05 15:30:03.076  1525  5177 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 15:30:03.123  1274 13550 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService started execution. cause:9 exec_start_elapsed_seconds: 9794 [CONTEXT service_id=218 ]
07-05 15:30:03.128  1274 14364 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService finished executing. cause:9 result: 1 elapsed_millis: 14 uptime_millis: 14 exec_start_elapsed_seconds: 9794 [CONTEXT service_id=218 ]
07-05 15:30:03.132  1274  1274 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 15:30:03.132  1274  1274 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 15:30:03.175 14522 14544 E TestRunner: failed: testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 15:30:03.175 14522 14544 E TestRunner: ----- begin exception -----
07-05 15:30:03.182 14522 14544 E TestRunner: androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 540, 1099 and precision: 16, 16' on view 'view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>'.
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:179)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 15:30:03.182 14522 14544 E TestRunner: Caused by: java.lang.ArrayIndexOutOfBoundsException: length=22; index=-1
07-05 15:30:03.182 14522 14544 E TestRunner: 	at java.util.ArrayList.get(ArrayList.java:439)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.kewtoms.whatappsdo.model.AppSearcher.AppSearcher$1.onPostSuccess(AppSearcher.java:197)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:667)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.kewtoms.whatappsdo.utils.RequestUtils$5.onResponse(RequestUtils.java:592)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.android.volley.toolbox.JsonRequest.deliverResponse(JsonRequest.java:100)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.android.volley.ExecutorDelivery$ResponseDeliveryRunnable.run(ExecutorDelivery.java:102)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.os.Looper.loop(Looper.java:288)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 15:30:03.182 14522 14544 E TestRunner: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 15:30:03.182 14522 14544 E TestRunner: ----- end exception -----
07-05 15:30:03.194 14522 14544 I TestRunner: finished: testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
