07-05 14:42:04.735 10254 10278 I TestRunner: started: testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
07-05 14:42:04.736  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 14:42:04.738  1543  1736 I AiAiEcho: Predicting[0]:
07-05 14:42:04.738  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 14:42:04.740  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:42:04.740  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:42:04.741  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:42:04.742  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:42:04.743  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:42:04.744  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:42:04.746  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:42:04.747  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:42:04.748   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:42:04.748  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:42:04.750   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:42:04.750   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:42:04.785 10254 10278 D APP:SecurePrefsManager: saveSignInData: run
07-05 14:42:04.786 10254 10278 D APP:SecurePrefsManager: saveAccessToken: run
07-05 14:42:04.786 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:04.809 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:04.978   796   966 D EGL_emulation: app_time_stats: avg=4953.72ms min=4953.72ms max=4953.72ms count=1
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:84)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:42:05.037 10254 10278 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn(AppSearcherTest.java:84)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:42:05.107 10254 10278 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:42:05.137 10254 10278 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 14:42:05.150 10254 10278 D APP:SecurePrefsManager: saveAccessToken: done
07-05 14:42:05.150 10254 10278 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 14:42:05.150 10254 10278 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 14:42:05.151 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:05.154 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:05.188 10254 10278 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 14:42:05.189 10254 10278 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 14:42:05.189 10254 10278 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 14:42:05.189 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:05.191 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:05.224 10254 10278 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 14:42:05.224 10254 10278 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 14:42:05.224 10254 10278 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 14:42:05.224 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:05.226 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:05.261 10254 10278 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 14:42:05.261 10254 10278 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 14:42:05.261 10254 10278 D APP:SecurePrefsManager: saveLoginTime: run
07-05 14:42:05.261 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:05.264 10254 10278 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:05.298 10254 10278 D APP:SecurePrefsManager: saveLoginTime: done
07-05 14:42:05.298 10254 10278 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 14:42:05.298 10254 10278 D APP:SecurePrefsManager: saveSignInData: done
07-05 14:42:05.489   336 10305 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:05.490 10254 10278 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 14:42:05.508   336 10307 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:05.528 10254 10278 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 14:42:05.559   796   930 D SplashScreenView: Build android.window.SplashScreenView{847e798 V.E...... ......ID 0,0-0,0}
07-05 14:42:05.559   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 14:42:05.559   796   930 D SplashScreenView: Branding: view: android.view.View{6be1bf1 G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 14:42:05.562   796   966 W Parcel  : Expecting binder but got null!
07-05 14:42:05.574  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 14:42:05.575 10254 10309 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 14:42:05.576 10254 10309 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 14:42:05.578 10254 10309 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 14:42:05.609  1166  1782 D EGL_emulation: app_time_stats: avg=2126.28ms min=135.86ms max=4116.70ms count=2
07-05 14:42:05.617   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:42:05.626   564  2525 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 14:42:05.626   564  2525 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 14:42:05.626   564  2525 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 14:42:05.626   564  2525 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 14:42:05.626   564  2525 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 14:42:05.626   564  2525 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 14:42:05.626   564  2525 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 14:42:05.626   564  2525 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 14:42:05.705   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4ad0
07-05 14:42:05.705   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4110
07-05 14:42:05.765   796   966 D EGL_emulation: app_time_stats: avg=2229487.75ms min=2229487.75ms max=2229487.75ms count=1
07-05 14:42:05.835 10254 10254 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 14:42:05.846 10254 10254 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 14:42:05.847 10254 10254 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 14:42:05.854   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d1590
07-05 14:42:05.969 10254 10254 D APP:MainActivity: onCreate: run
07-05 14:42:05.971 10254 10254 D APP:Constants: initializeData: run
07-05 14:42:05.972 10254 10254 D APP:Constants: initializeData: done
07-05 14:42:06.136   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0c90
07-05 14:42:06.551 10254 10254 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10196; state: ENABLED
07-05 14:42:06.583 10254 10254 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 14:42:06.583 10254 10254 I APP:MainActivity: onCreate: done setting root view
07-05 14:42:06.603 10254 10254 I APP:MainActivity: onCreate: Done initializing drawer
07-05 14:42:06.654 10254 10254 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 14:42:07.132 10254 10254 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 216.440ms (1284.42 bytecodes/s) (8240B approximate peak alloc)
07-05 14:42:07.166 10254 10254 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 14:42:07.166 10254 10254 D APP:MainActivity: onCreate: Done
07-05 14:42:07.167 10254 10254 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 14:42:07.167 10254 10254 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:42:07.172 10254 10254 D APP:HomeFragment: onCreateView: run
07-05 14:42:07.259 10254 10254 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 14:42:07.260 10254 10254 I CodeRunManager: Feature HomeFragment.runThreadCheckAndCachePackagesRelated not found in config. Using default value: true
07-05 14:42:07.261 10254 10254 I APP:HomeFragment: silentSignIn: run
07-05 14:42:07.261 10254 10312 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: Cache directories created successfully
07-05 14:42:07.462 10254 10312 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 144.262ms (221.82 bytecodes/s) (2528B approximate peak alloc)
07-05 14:42:07.705 10254 10312 I APP:HomeFragment: sending get request to get_is_server_online_link: http://localhost:8000/__mock_get_is_server_online
07-05 14:42:07.737 10254 10254 D APP:Authenticator: silentSignIn: run
07-05 14:42:07.737 10254 10254 D APP:Authenticator: hasAccountStored: run
07-05 14:42:07.737 10254 10254 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:42:07.739 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:07.745 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:07.778   336 10313 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:07.779 10254 10312 D TrafficStats: tagSocket(103) with statsTag=0xffffffff, statsUid=-1
07-05 14:42:07.784 10254 10306 D TrafficStats: tagSocket(105) with statsTag=0xffffffff, statsUid=-1
07-05 14:42:07.799 10254 10254 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:42:07.800 10254 10254 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:42:07.800 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:07.804 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:07.827   336 10317 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:07.854 10254 10254 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:42:07.855 10254 10254 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:42:07.855 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:07.857 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:07.897 10254 10254 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:42:07.898 10254 10254 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 14:42:07.898 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:07.903 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:07.952 10254 10254 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 14:42:07.952 10254 10254 D APP:Authenticator: hasAccountStored: end: true
07-05 14:42:07.952 10254 10254 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:42:07.952 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:07.957 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:07.995 10254 10254 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:42:07.995 10254 10254 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:42:07.995 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:07.998 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:08.002 10254 10318 I APP:ScanAppCallable: run: pool-3-thread-1
07-05 14:42:08.002 10254 10318 I System.out: Directories created successfully
07-05 14:42:08.036 10254 10254 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:42:08.036 10254 10254 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:42:08.036 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:08.039 10254 10254 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:08.043 10254 10318 I APP:ScanAppCallable: converting com.android.camera2 icon drawable to bitmap
07-05 14:42:08.053 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.069 10254 10318 I APP:ScanAppCallable: converting com.android.chrome icon drawable to bitmap
07-05 14:42:08.075 10254 10254 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:42:08.081 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.095 10254 10318 I APP:ScanAppCallable: converting com.android.settings icon drawable to bitmap
07-05 14:42:08.104 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.118 10254 10318 I APP:ScanAppCallable: converting com.google.android.apps.docs icon drawable to bitmap
07-05 14:42:08.123 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.129 10254 10254 D APP:Authenticator: validateAccessToken: run
07-05 14:42:08.129 10254 10254 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 14:42:08.129 10254 10254 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:42:08.132 10254 10254 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:42:08.132 10254 10319 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:42:08.133 10254 10319 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:42:08.143 10254 10318 I APP:ScanAppCallable: converting com.google.android.apps.maps icon drawable to bitmap
07-05 14:42:08.152 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.168 10254 10318 I APP:ScanAppCallable: converting com.google.android.apps.messaging icon drawable to bitmap
07-05 14:42:08.177 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.178 10254 10319 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:42:08.179 10254 10254 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:42:08.179 10254 10254 D APP:Authenticator: validateAccessToken: end
07-05 14:42:08.181 10254 10254 D APP:Authenticator: silentSignIn: done
07-05 14:42:08.181 10254 10254 D APP:HomeFragment: silentSignIn: done
07-05 14:42:08.182 10254 10254 D APP:HomeFragment: onCreateView: done
07-05 14:42:08.182 10254 10324 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:42:08.184 10254 10324 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:08.187 10254 10324 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:08.192 10254 10318 I APP:ScanAppCallable: converting com.google.android.apps.photos icon drawable to bitmap
07-05 14:42:08.193 10254 10254 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 14:42:08.194 10254 10254 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:42:08.199 10254 10254 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 14:42:08.199 10254 10254 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:42:08.201 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.208 10254 10254 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10196; state: DISABLED
07-05 14:42:08.215 10254 10308 W Parcel  : Expecting binder but got null!
07-05 14:42:08.219 10254 10318 I APP:ScanAppCallable: converting com.google.android.apps.youtube.music icon drawable to bitmap
07-05 14:42:08.231 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.245 10254 10324 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:42:08.249 10254 10318 I APP:ScanAppCallable: converting com.google.android.calendar icon drawable to bitmap
07-05 14:42:08.254   336 10327 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:08.263 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.278 10254 10318 I APP:ScanAppCallable: converting com.google.android.contacts icon drawable to bitmap
07-05 14:42:08.282   398   428 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10196 pid=0
07-05 14:42:08.282   398   428 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10196 => denied (574 us)
07-05 14:42:08.282   398   428 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10196 pid=0
07-05 14:42:08.283   398   428 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10196 => denied (151 us)
07-05 14:42:08.283   398   427 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10196 pid=10254
07-05 14:42:08.283   398   427 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10196 => denied (406 us)
07-05 14:42:08.283   398   428 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10196 pid=0
07-05 14:42:08.283   398   428 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10196 => denied (339 us)
07-05 14:42:08.283   398   427 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10196 pid=10254
07-05 14:42:08.284   398   427 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10196 => denied (984 us)
07-05 14:42:08.287 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.294 10254 10254 E RecyclerView: No adapter attached; skipping layout
07-05 14:42:08.296 10254 10308 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:42:08.299 10254 10308 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 14:42:08.299 10254 10308 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 14:42:08.303   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3ab0
07-05 14:42:08.304   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2f70
07-05 14:42:08.319   796   966 D EGL_emulation: app_time_stats: avg=3340.55ms min=3340.55ms max=3340.55ms count=1
07-05 14:42:08.319 10254 10318 I APP:ScanAppCallable: converting com.google.android.deskclock icon drawable to bitmap
07-05 14:42:08.321 10254 10308 D EGL_emulation: eglCreateContext: 0x720c5f5e5610: maj 3 min 1 rcv 4
07-05 14:42:08.339 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.357 10254 10318 I APP:ScanAppCallable: converting com.google.android.dialer icon drawable to bitmap
07-05 14:42:08.359 10254 10308 D EGL_emulation: eglMakeCurrent: 0x720c5f5e5610: ver 3 1 (tinfo 0x720e7729f080) (first time)
07-05 14:42:08.387   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 14:42:08.387 10254 10308 I Gralloc4: mapper 4.x is not supported
07-05 14:42:08.391 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.392   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 14:42:08.393   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 14:42:08.394 10254 10308 W Gralloc4: allocator 4.x is not supported
07-05 14:42:08.405 10254 10318 I APP:ScanAppCallable: converting com.google.android.gm icon drawable to bitmap
07-05 14:42:08.410 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.415 10254 10308 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:42:08.415 10254 10308 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:42:08.428 10254 10318 I APP:ScanAppCallable: converting com.google.android.youtube icon drawable to bitmap
07-05 14:42:08.445 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.459 10254 10318 I APP:ScanAppCallable: converting com.android.stk icon drawable to bitmap
07-05 14:42:08.478 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.495 10254 10318 I APP:ScanAppCallable: converting com.google.android.documentsui icon drawable to bitmap
07-05 14:42:08.508 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.520 10254 10318 I APP:ScanAppCallable: converting com.google.android.googlequicksearchbox icon drawable to bitmap
07-05 14:42:08.525 10254 10318 I APP:HomeFragment: onScanning: pool-3-thread-1
07-05 14:42:08.528   564   602 W ziparchive: Unable to open '/data/app/~~Sp3clThBj_2S1ZtmBivAbg==/com.kewtoms.whatappsdo-Km_HElkAitXAM3S6CYjpCQ==/base.dm': No such file or directory
07-05 14:42:08.529   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +2s989ms
07-05 14:42:08.548 10254 10312 D APP:AppScraper: checkAppNeedScrape: disabled
07-05 14:42:08.548 10254 10312 D APP:AppScraper: scrapeWhenNeeded: disabled
07-05 14:42:08.548 10254 10312 D APP:AppScraper: sendScrapeData: disabled
07-05 14:42:08.548 10254 10312 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 14:42:08.551   564  2044 W InputManager-JNI: Input channel object '594c123 Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 14:42:08.562 10254 10254 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:42:08.562 10254 10254 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:42:08.563 10254 10254 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 14:42:08.563 10254 10254 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 14:42:08.563 10254 10254 I APP:Authenticator: silentSignIn: Validate success. Signing in..
07-05 14:42:08.565 10254 10254 D APP:Authenticator: signInUsingAccessToken:run
07-05 14:42:08.565 10254 10254 I APP:Authenticator: signInUsingAccessToken:: Sending login request:http://localhost:8000/__mock_sign_in_access_token
07-05 14:42:08.566 10254 10254 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:42:08.566 10254 10254 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:42:08.567 10254 10329 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:42:08.570 10254 10329 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:42:08.570   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3630
07-05 14:42:08.571   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d50d0
07-05 14:42:08.571   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4350
07-05 14:42:08.571   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4350
07-05 14:42:08.571   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4350
07-05 14:42:08.571   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3630
07-05 14:42:08.571   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d50d0
07-05 14:42:08.573 10254 10329 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:42:08.574 10254 10254 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:42:08.575 10254 10254 D APP:Authenticator: signInUsingAccessToken:end
07-05 14:42:08.577 10254 10254 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 14:42:08.586 10254 10331 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:42:08.587 10254 10331 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:08.591  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 14:42:08.596 10254 10331 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:08.604   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2f10
07-05 14:42:08.604   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d27f0
07-05 14:42:08.621 10254 10254 D InsetsController: show(ime(), fromIme=false)
07-05 14:42:08.622   796   966 D EGL_emulation: app_time_stats: avg=2856.58ms min=2856.58ms max=2856.58ms count=1
07-05 14:42:08.636  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.637  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.638  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null}, false)
07-05 14:42:08.645  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.646  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:42:08.646  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 14:42:08.651  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 14:42:08.653  1371  1371 I AndroidIME: AbstractIme.onActivate():86 PasswordIme.onActivate() : EditorInfo = inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 14:42:08.668 10254 10331 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:42:08.673   336 10337 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:08.676 10254 10331 E Volley  : [82] NetworkUtility.shouldRetryException: Unexpected response code 404 for http://localhost:8000/__mock_sign_in_access_token
07-05 14:42:08.677  1371  1381 W putmethod.latin: Reducing the number of considered missed Gc histogram windows from 224 to 100
07-05 14:42:08.692 10254 10254 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10196; state: ENABLED
07-05 14:42:08.694 10254 10254 I AssistStructure: Flattened final assist data: 2432 bytes, containing 1 windows, 15 views
07-05 14:42:08.695  1274  2311 I FontLog : Received query Google Sans Text, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:42:08.696  1274  2311 I FontLog : Query [Google Sans Text] resolved to {Google Sans Text, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:08.696 10254 10254 E APP:RequestUtils: getJsonObjectRequest.onErrorResponse: VolleyError: com.android.volley.ClientError
07-05 14:42:08.698 10254 10254 I APP:Authenticator: signInUsingAccessToken:: Login using access token failed
07-05 14:42:08.698  1274  2311 I FontLog : Font PFD returned from cache for {Google Sans Text, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:08.699  1274  2311 I FontLog : Fetch {Google Sans Text, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:42:08.703  1274  2311 I FontLog : Pulling font file for id = 36, cache size = 5 [CONTEXT service_id=132 ]
07-05 14:42:08.710  1371  1371 D CompatibilityChangeReporter: Compat change id reported: 171228096; UID 10135; state: ENABLED
07-05 14:42:08.758  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 14:42:08.759  1274  1290 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:42:08.760  1274  1290 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:08.762  1274  1290 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:42:08.778  1274  1290 I FontLog : Pulling font file for id = 37, cache size = 5 [CONTEXT service_id=132 ]
07-05 14:42:08.789  1274  1290 I FontLog : Pulling font file for id = 37, cache size = 5 [CONTEXT service_id=132 ]
07-05 14:42:08.834  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:42:08.835  1371  1371 I SoftKeyboardView: SoftKeyboardView.setMaxHeight():731 Set max keyboard height:1804.
07-05 14:42:08.837  1274  1290 I FontLog : Received query Google Sans Text:500, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:42:08.838  1274  1290 I FontLog : Query [Google Sans Text:500] resolved to {Google Sans Text, wdth 100.0, wght 500, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:08.841  1274  1290 I FontLog : Font PFD returned from cache for {Google Sans Text, wdth 100.0, wght 500, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:08.842  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_MANDATORY
07-05 14:42:08.843  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:42:08.845  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 14:42:08.845  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:42:08.848  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 14:42:08.849  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 14:42:08.850  1274  1290 I FontLog : Fetch {Google Sans Text, wdth 100.0, wght 500, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:42:08.854  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.855  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.856  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.856  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:42:08.856  1274  1290 I FontLog : Pulling font file for id = 38, cache size = 6 [CONTEXT service_id=132 ]
07-05 14:42:08.858  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.867  1371  1371 I KeyboardManager: KeyboardManager.requestKeyboard():128 Using cached keyboard prime, imeId=password
07-05 14:42:08.869  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.871  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.871  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 14:42:08.872  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:42:08.872  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:42:08.873  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 14:42:08.877 10254 10278 W FileTestStorage: Output properties is not supported.
07-05 14:42:08.877  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 14:42:08.882  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 14:42:08.881 10254 10278 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 14:42:08.883  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 14:42:08.884  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.firebase.dynamiclinks.service.START pkg=com.google.android.gms }
07-05 14:42:08.892  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 PasswordIme.onDeactivate()
07-05 14:42:08.902  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 14:42:08.904  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 14:42:08.907  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=********** fieldName=null extras=null}, false)
07-05 14:42:08.907  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 14:42:08.909 10254 10254 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F...... 265,536-815,683 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
07-05 14:42:08.911  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:42:08.916  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInputView():1995 onStartInputView(EditorInfo{inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=********** fieldName=null extras=null}, false)
07-05 14:42:08.917  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:42:08.918  1371  1643 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:08.919  1371  1371 W ModuleManager: ModuleManager.loadModule():450 Module erj is not available
07-05 14:42:08.922  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.getKeyboardHeightRatio():189 systemKeyboardHeightRatio:1.000000; userKeyboardHeightRatio:1.000000.
07-05 14:42:08.930  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 14:42:08.937  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 14:42:08.938  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 14:42:08.945  1371  1371 I LatinIme: LatinIme.<init>():328 Language = en-US
07-05 14:42:08.947  1371  1643 W MetricsManager: MetricsManager.addProcessorImpl():614 Processor dzr@5a9708c already exists.
07-05 14:42:08.949 10254 10278 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 14:42:08.949 10254 10278 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 14:42:08.950 10254 10278 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 14:42:08.951 10254 10278 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 14:42:08.964  1371  1371 I Delight5Facilitator: Delight5Facilitator.initializeForIme():777 initializeForIme() : Locale = [en_US], layout = qwerty
07-05 14:42:08.965  1371  1371 I Delight5Facilitator: Delight5Facilitator.resetDecoder():954 resetDecoder() : Locale = [en_US]
07-05 14:42:08.967  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:1499600484
07-05 14:42:08.974  1274  1290 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef616670 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 14:42:08.979  1371  1371 I Delight5Decoder: Delight5DecoderWrapper.createOrResetDecoder():341 Decoder reset
07-05 14:42:09.033 10254 10278 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 14:42:09.079 10254 10254 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:42:09.079 10254 10254 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:42:09.080 10254 10254 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 14:42:09.101  1371  1805 I MainLanguageModelLoader: MainLanguageModelLoader.call():145 Running LM loader for [en_US]
07-05 14:42:09.105 10254 10254 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 14:42:09.105 10254 10254 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:42:09.105 10254 10254 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:42:09.107  1371  1371 I AndroidIME: AbstractIme.onActivate():86 LatinIme.onActivate() : EditorInfo = inputType=0x1(Normal) imeOptions=0x3 privateImeOptions=null actionName=SEARCH actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=non-empty label=null packageName=com.kewtoms.whatappsdo fieldId=********** fieldName=null extras=null, IncognitoMode = false, DeviceLocked = false
07-05 14:42:09.110  1371  1804 I SuperDelight: SuperDelightBundledMetadataParser.addSystemLms():200 system lm dir /system/usr/share/ime/google/d3_lms does not exist or is not readable
07-05 14:42:09.111  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.cancelShutdown():55 cancelShutdown()
07-05 14:42:09.112  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.syncLanguagePacks():67 syncLanguagePacks()
07-05 14:42:09.113  1371  1805 I DelightDataFileManagerLanguageModelProvider: DelightDataFileManagerLanguageModelProvider.fetchLanguageModel():73 find data en-840 delight
07-05 14:42:09.121  1371  1805 I SuperDelight: SuperDelightManager.syncBundledLanguageModels():734 syncBundledLanguageModels(): clearing bundled_delight selection
07-05 14:42:09.126 10254 10254 I ViewInteraction: Performing 'type text(eat)' action on view view.getId() is <**********/com.kewtoms.whatappsdo:id/search_field>
07-05 14:42:09.126  1371 10352 I SpeechFactory: SpeechRecognitionFactory.maybeScheduleAutoPackDownloadForFallback():205 maybeScheduleAutoPackDownloadForFallback()
07-05 14:42:09.127  1371 10352 I FallbackOnDeviceRecognitionProvider: FallbackOnDeviceRecognitionProvider.maybeScheduleAutoPackDownload():195 maybeScheduleAutoPackDownload() for language tag en-US
07-05 14:42:09.129  1371 10351 I JobSchedulerImpl: JobSchedulerImpl.cancel():125 Cancel task: DlamTrainingTask. Not pending.
07-05 14:42:09.130  1371  1371 I LatinIme: LatinIme.updateEnableInlineSuggestionsOnDecoderSideFlags():1003 inline flag updated to:false
07-05 14:42:09.131  1371  1805 I LmManager: LmManager.asyncUpdateEnabledLanguageModels():90 asyncUpdateEnabledLanguageModels()
07-05 14:42:09.137  1371  1805 I SuperDelight: SuperDelightManager.initializeDelightSuperpacks():340 initializeDelightSuperpacks()
07-05 14:42:09.137  1371  1804 I SuperDelight: SuperDelightBundledMetadataParser.parse():190 SuperDelightBundledMetadataParser#parse(): manifest parsed with 1 packs
07-05 14:42:09.140  1371  1805 I SuperDelight: SuperDelightManager.getDelightMetadataUriAndVersion():1011 getDelightMetadataUriAndVersion(): Phenotype : ********** : https://www.gstatic.com/android/keyboard/dictionarypack/Omei-normal/metadata.json
07-05 14:42:09.141  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=884.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=6920137, downTime=6920137, deviceId=-1, source=0x1002, displayId=0, eventId=-918245402 }
07-05 14:42:09.145  1371  1805 I SuperDelight: SuperDelightAppsSuperpacksManager.initializeDelightAppsSuperpacks():92 initializeDelightAppsSuperpacks()
07-05 14:42:09.148  1371  1805 I SuperDelight: SuperDelightManager.initializeOverridesSuperpacks():402 initializeOverridesSuperpacks()
07-05 14:42:09.152  1371  1805 I DelightDataFileManagerLanguageModelProvider: DelightDataFileManagerLanguageModelProvider.fetchLanguageModel():73 find data en-US delight
07-05 14:42:09.154  1371 10342 I SuperDelight: SuperDelightManager.initializeBundledDelightSuperpacks():376 initializeBundledDelightSuperpacks()
07-05 14:42:09.155  1371  1805 I LmManager: LmManager.asyncUpdateEnabledLanguageModels():90 asyncUpdateEnabledLanguageModels()
07-05 14:42:09.155  1371  1805 I SuperDelight: SuperDelightManager.initializeDelightSuperpacks():340 initializeDelightSuperpacks()
07-05 14:42:09.157  1371  1805 I SuperDelight: SuperDelightManager.getDelightMetadataUriAndVersion():1011 getDelightMetadataUriAndVersion(): Phenotype : ********** : https://www.gstatic.com/android/keyboard/dictionarypack/Omei-normal/metadata.json
07-05 14:42:09.158  1371  1805 I SuperDelight: SuperDelightAppsSuperpacksManager.initializeDelightAppsSuperpacks():92 initializeDelightAppsSuperpacks()
07-05 14:42:09.158  1371  1805 I SuperDelight: SuperDelightManager.initializeOverridesSuperpacks():402 initializeOverridesSuperpacks()
07-05 14:42:09.161  1371  1805 I MainLanguageModelLoader: MainLanguageModelLoader.updateLmAvailableState():392 updateLmAvailableState(): locale? en_US prevState? AVAILABLE
07-05 14:42:09.161  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():649 loadLanguageModel() : 1, version [2022102600]
07-05 14:42:09.165  1371  1805 I MainLanguageModelLoader: MainLanguageModelLoader.updateLmAvailableState():428 updateLmAvailableState(): locale? en_US newState? AVAILABLE
07-05 14:42:09.169  1371  1805 I Delight5Facilitator: BlocklistLoader.call():37 Running blocklist loader
07-05 14:42:09.174  1371  1805 I Delight5Facilitator: ContactsLanguageModelLoader.call():35 Running contacts language model loader
07-05 14:42:09.176  1371  1805 I Delight5Facilitator: ContactsLanguageModelLoader.call():47 Contacts language model is unloaded: preferences=false, deviceLocked=false
07-05 14:42:09.180  1371  1805 I Delight5Facilitator: PersonalLanguageModelLoader.call():44 Running personal language model loader
07-05 14:42:09.186  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=884.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=6920183, downTime=6920137, deviceId=-1, source=0x1002, displayId=0, eventId=-602192732 }
07-05 14:42:09.186  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=false)
07-05 14:42:09.192  1371  1805 I Delight5Facilitator: UserHistoryLanguageModelLoader.call():82 Running user history language model loader
07-05 14:42:09.194 10254 10254 D InputMethodManager: showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{1fde08b VFED..CL. .F.P..ID 265,536-815,683 #7f0801b2 app:id/search_field aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
07-05 14:42:09.197  1371  1805 I EmojiSuperpacksManager: EmojiSuperpacksManager.triggerSync():374 triggerSync() : isBundled = false
07-05 14:42:09.201  1371  1805 I EmojiSuperpacksManager: EmojiSuperpacksManager.triggerSync():374 triggerSync() : isBundled = true
07-05 14:42:09.204  1371  1371 I KeyboardManager: KeyboardManager.requestKeyboard():146 Creating keyboard prime, imeId=ime_english_united_states, cacheKey=theme_border_bottom4dp_stylesheet_noshadow_gsans_pillkey_silkpopup_refinedkp_materiallight_phone
07-05 14:42:09.205  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.getEmojiShortcuts():114 Emoji shortcut file does not exist.
07-05 14:42:09.205  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.call():63 0 emoji shortcut maps loaded.
07-05 14:42:09.206  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.call():72 Finished loading emoji shortcuts
07-05 14:42:09.207  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.208  1371  1805 I Delight5Facilitator: EmailLanguageModelLoader.call():30 Running email language model loader
07-05 14:42:09.208  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.209  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():364 setInputView() : supportsOneHandedMode=true
07-05 14:42:09.210  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:42:09.211  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:42:09.212  1371  1805 I Delight5Facilitator: KeyCorrectionTfliteModelLoader.call():29 Running Key Correction Tflite Model loader
07-05 14:42:09.213  1371  1371 I KeyboardModeManager: KeyboardModeManager.setInputView():356 setInputView() : entry=hwn{languageTag=en-US, variant=qwerty, hasLocalizedResources=true, conditionCacheKey=_device=phone_device_size=default_enable_adaptive_text_editing=true_enable_inline_suggestions_on_client_side=false_enable_large_tablet_batch_1=false_enable_large_tablet_batch_2=false_enable_more_candidates_view_for_multilingual=false_enable_nav_redesign=false_enable_number_row=false_enable_preemptive_decode=true_enable_secondary_symbols=false_expressions=normal_four_or_more_letter_rows=false_keyboard_mode=normal_language=en-US_orientation=portrait_physical_keyboard=qwerty_show_secondary_digits=true_show_suggestions=true_split_with_duplicate_keys=true_variant=qwerty, imeDef.stringId=ime_english_united_states, imeDef.className=com.google.android.apps.inputmethod.libs.latin5.LatinIme, imeDef.languageTag=en-US}
07-05 14:42:09.215  1371  1805 I Delight5Facilitator: RankingModelLoader.call():28 Running Ranking Model loader
07-05 14:42:09.221  1371  1804 I SuperDelight: SuperDelightDownloadMetadataParser.parse():185 SuperDelightDownloadMetadataParser#parse(delight): Manifest parsed with 885 packs
07-05 14:42:09.223  1371  1371 I VoiceImeExtension: VoiceImeExtension.shouldStartVoiceInputAutomatically():383 No private IME option set to start voice input.
07-05 14:42:09.226  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.228  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.230  1371  1371 I Choreographer: Skipped 34 frames!  The application may be doing too much work on its main thread.
07-05 14:42:09.250  1371 10344 I SuperDelight: SuperDelightManager.lambda$registerAndUpgradeSuperpacks$5():476 SuperDelightManager#registerAndUpgradeSuperpacks(delight): current **********, required **********
07-05 14:42:09.253  1371 10342 I SuperDelight: SuperDelightManager.lambda$registerAndUpgradeSuperpacks$5():476 SuperDelightManager#registerAndUpgradeSuperpacks(delight): current **********, required **********
07-05 14:42:09.255  1371 10343 I SuperDelight: SuperDelightManager.lambda$syncOverridesLanguageModels$14():689 SuperDelightManager#syncOverridesLanguageModels(): Superpack delight_overrides not found or negative version provided
07-05 14:42:09.257  1371 10342 I SuperDelight: SuperDelightManager.lambda$syncOverridesLanguageModels$14():689 SuperDelightManager#syncOverridesLanguageModels(): Superpack delight_overrides not found or negative version provided
07-05 14:42:09.263  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBackgroundFrameVisibility():357 Set background frame visibility. old:8, new:8
07-05 14:42:09.283  1371  1805 I CrankEngineLocales: CrankEngineLocales.getLocaleToUseForCrankEngine():66 Using locale en_US for emoji prediction
07-05 14:42:09.283  1371  1805 I CrankEngineLocales: CrankEngineLocales.getLocaleToUseForCrankEngine():66 Using locale en_US for emoji prediction
07-05 14:42:09.284  1371  1805 I CrankEngineLocales: CrankEngineLocales.getLocaleToUseForCrankEngine():66 Using locale en_US for emoji prediction
07-05 14:42:09.297  1371  1939 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:42:09.337  1371  1804 I SP      : Registering bundled_delight.2022101700, url: null, constraints: *:*:*:*, flags: *, requested: 2022101700, current: 2022101700
07-05 14:42:09.341  1371  1804 I SP      : Registering emoji.2023020111, url: https://www.gstatic.com/android/keyboard/modelpack/emoji/20230201113008/superpacks_manifest.zip, constraints: m:*:*:*, flags: *, requested: 2023020111, current: 2023020111
07-05 14:42:09.341  1371 10357 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:42:09.343  1371  1381 I putmethod.latin: Background concurrent copying GC freed 90095(4041KB) AllocSpace objects, 15(616KB) LOS objects, 49% free, 9037KB/17MB, paused 9.613ms,208us total 106.271ms
07-05 14:42:09.346  1371  1804 I SP      : Registering bundled_emoji.2023020111, url: null, constraints: m:*:*:*, flags: *, requested: 2023020111, current: 2023020111
07-05 14:42:09.349  1371 10357 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 14:42:09.353  1371 10357 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 14:42:09.354  1371  1804 I SP      : Registering kc_tflite_model.20220919, url: https://www.gstatic.com/android/keyboard/kc_triggering_model/superpacks-manifest-20220805_235142.json, constraints: W:*:*:*, flags: bg, requested: 20220919, current: 20220919
07-05 14:42:09.359  1371  1804 I SP      : Registering kc_tflite_model.20220919, url: https://www.gstatic.com/android/keyboard/kc_triggering_model/superpacks-manifest-20220805_235142.json, constraints: W:*:*:*, flags: bg, requested: 20220919, current: 20220919
07-05 14:42:09.361  1371  1804 I SP      : Registering kc_tflite_model.20220919, url: https://www.gstatic.com/android/keyboard/kc_triggering_model/superpacks-manifest-20220805_235142.json, constraints: W:*:*:*, flags: bg, requested: 20220919, current: 20220919
07-05 14:42:09.366  1371 10342 I SuperDelight: SuperDelightManager.initializeBundledDelightSuperpacks():376 initializeBundledDelightSuperpacks()
07-05 14:42:09.369  1371 10357 D EGL_emulation: eglCreateContext: 0x720c5f60ea10: maj 3 min 1 rcv 4
07-05 14:42:09.372  1371  2057 I tflite  : Initialized TensorFlow Lite runtime.
07-05 14:42:09.374  1371 10343 I SuperDelight: SuperDelightManager.lambda$syncDownloadableLanguageModels$8():568 SuperDelightManager#syncDownloadableLanguageModels(delight): [OnDevice] Syncing for version **********
07-05 14:42:09.380  1371 10344 I SuperDelight: SuperDelightManager.lambda$syncDownloadableLanguageModels$8():568 SuperDelightManager#syncDownloadableLanguageModels(delight): [OnDevice] Syncing for version **********
07-05 14:42:09.381  1371 10345 I SuperDelight: SuperDelightManager$2.onSuccess():635 delight_overrides sync success, adding to data file manager
07-05 14:42:09.384  1371 10343 I SuperDelight: SuperDelightManager$2.onSuccess():635 delight_overrides sync success, adding to data file manager
07-05 14:42:09.396  1371  1805 I CrankEngineLocales: CrankEngineLocales.getLocaleToUseForCrankEngine():66 Using locale en_US for emoji prediction
07-05 14:42:09.396  1371  1805 I CrankEngineLocales: CrankEngineLocales.getLocaleToUseForCrankEngine():66 Using locale en_US for emoji prediction
07-05 14:42:09.403  1371 10357 D EGL_emulation: eglMakeCurrent: 0x720c5f60ea10: ver 3 1 (tinfo 0x720e8a211100) (first time)
07-05 14:42:09.408  1371  1804 I SP      : Registering bundled_delight.2022101700, url: null, constraints: *:*:*:*, flags: *, requested: 2022101700, current: 2022101700
07-05 14:42:09.411  1371 10343 I SuperDelight: SuperDelightManager.lambda$syncBundledLanguageModels$16():774 SuperDelightManager#syncBundledLanguageModels(): Syncing for version 2022101700
07-05 14:42:09.414  1371  1371 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:42:09.420  1371  1805 I CrankEngineLocales: CrankEngineLocales.getLocaleToUseForCrankEngine():66 Using locale en_US for emoji prediction
07-05 14:42:09.434  1371  1804 I EmojiSlicingStrategy: EmojiSlicingStrategy.matchEnabledLocalesWithPackLocales():127 matchEnabledLocalesWithPackLocales() : User enabled 1 locales; returning 1 slices
07-05 14:42:09.435  1371  1804 I SP      : Syncing emoji (2023020111) with slices: [{60932fe37d5f6d7a3e60488d8a1755c4, m:*:*:*}], metadata: false
07-05 14:42:09.439  1371  1804 I EmojiSlicingStrategy: EmojiSlicingStrategy.matchEnabledLocalesWithPackLocales():127 matchEnabledLocalesWithPackLocales() : User enabled 1 locales; returning 1 slices
07-05 14:42:09.439  1371  1804 I SP      : Syncing bundled_emoji (2023020111) with slices: [{7b9ddb0b8c001e17cee03b1edf554b20, m:*:*:*}], metadata: false
07-05 14:42:09.446 10254 10254 D UiControllerImpl: Injecting string: "eat"
07-05 14:42:09.453  1371  1804 I SP      : Syncing kc_tflite_model (20220919) with slices: [{tflite-kc-en_us_secagg-20220805_235142, m:*:*:*, fg}], metadata: false
07-05 14:42:09.465  1371  1804 I SP      : Syncing kc_tflite_model (20220919) with slices: [{tflite-kc-en_us_secagg-20220805_235142, m:*:*:*, fg}], metadata: false
07-05 14:42:09.471  1371  1804 I SP      : Syncing kc_tflite_model (20220919) with slices: [{tflite-kc-en_us_secagg-20220805_235142, m:*:*:*, fg}], metadata: false
07-05 14:42:09.473  1371 10344 I SuperDelight: SuperDelightManager.lambda$syncBundledLanguageModels$16():774 SuperDelightManager#syncBundledLanguageModels(): Syncing for version 2022101700
07-05 14:42:09.478  1371  1804 I SuperDelight: SuperDelightDownloadSlicingStrategy.getSlices():68 DownloadSlicing#getSlices() : Locale = [en_US]
07-05 14:42:09.488  1371  1804 I SuperDelight: SuperDelightDownloadSlicingStrategy.getSlices():125 DownloadSlicing#getSlices(): result {slices=[{delight:main_en_840_2022102600_1, m:*:*:*, fg}], last batch=true, sync metadata=false}
07-05 14:42:09.489  1371  1804 I SP      : Syncing delight (**********) with slices: [{main_en_840_2022102600_1, m:*:*:*, fg}], metadata: false
07-05 14:42:09.493  1371  2057 E native  : E0000 00:00:1751726529.481995    2057 scoped-file.cc:106] open() failed for /data/user/0/com.google.android.inputmethod.latin/files/personal/adapt_state.en.dat: No such file or directory [2]
07-05 14:42:09.495  1371  1804 I SuperDelight: SuperDelightDownloadSlicingStrategy.getSlices():68 DownloadSlicing#getSlices() : Locale = [en_US]
07-05 14:42:09.497  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():671 Loaded main LM 1.en
07-05 14:42:09.498  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():649 loadLanguageModel() : 7, version [n/a]
07-05 14:42:09.500  1371  1804 I SuperDelight: SuperDelightDownloadSlicingStrategy.getSlices():125 DownloadSlicing#getSlices(): result {slices=[{delight:main_en_840_2022102600_1, m:*:*:*, fg}], last batch=true, sync metadata=false}
07-05 14:42:09.500  1371  1804 I SP      : Syncing delight (**********) with slices: [{main_en_840_2022102600_1, m:*:*:*, fg}], metadata: false
07-05 14:42:09.501  1371  2057 I native  : I0000 00:00:1751726529.501830    2057 blocklist-assembly.cc:121] LoadBlocklist en
07-05 14:42:09.504  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():690 Loaded dynamic LM 7.en
07-05 14:42:09.505  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():649 loadLanguageModel() : 4, version [n/a]
07-05 14:42:09.509  1371  1804 I SuperDelight: SuperDelightBundledSlicingStrategy.getSlices():39 BundledSlicing#getSlices() : Locale = [en_US]
07-05 14:42:09.509  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():690 Loaded dynamic LM 4.en
07-05 14:42:09.510  1371  1804 I SuperDelight: SuperDelightBundledSlicingStrategy.getSlices():65 BundledSlicing#getSlices(): result {slices=[bundled_delight:main_en_us_2022051700_2], last batch=true, sync metadata=false}
07-05 14:42:09.510  1371  1804 I SP      : Syncing bundled_delight (2022101700) with slices: [main_en_us_2022051700_2], metadata: false
07-05 14:42:09.510  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():649 loadLanguageModel() : 3, version [n/a]
07-05 14:42:09.516  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.loadLanguageModel():690 Loaded dynamic LM 3.en
07-05 14:42:09.560  1371  1805 I CrankEngineLocales: CrankEngineLocales.getLocaleToUseForCrankEngine():66 Using locale en_US for emoji prediction
07-05 14:42:09.597  1371  1380 I putmethod.latin: Compiler allocated 4255KB to compile void nqm.h(java.lang.Object, nqv, nol)
07-05 14:42:09.671  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=1, keyboard existence=false)
07-05 14:42:09.678  1371  1388 I OpenGLRenderer: Davey! duration=1013ms; Flags=1, FrameTimelineVsyncId=2862460, IntendedVsync=6919651514606, Vsync=6920218181250, InputEventId=0, HandleInputStart=6920232020800, AnimationStart=6920232038000, PerformTraversalsStart=6920232061100, DrawStart=6920299539900, FrameDeadline=6919668181272, FrameInterval=6920230711300, FrameStartTime=16666666, SyncQueued=6920525175000, SyncStart=6920526904700, IssueDrawCommandsStart=6920527633100, SwapBuffers=6920626329000, FrameCompleted=6920667211800, DequeueBufferDuration=38841500, QueueBufferDuration=638500, GpuCompleted=6920635660700, SwapBuffersCompleted=6920667211800, DisplayPresentTime=0, CommandSubmissionCompleted=6920626329000,
07-05 14:42:09.683 10254 10308 D EGL_emulation: app_time_stats: avg=164.25ms min=4.51ms max=437.95ms count=7
07-05 14:42:09.683  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.687   564  2525 D InputDispatcher: Touch mode switch rejected, caller (pid=0, uid=10196) doesn't own the focused window nor none of the previously interacted window
07-05 14:42:09.689  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.704  1371  1643 I LstmExtension: LstmExtension.onCreate():110 onCreate(): Finished in 1 ms
07-05 14:42:09.709  1371 10359 W JobInfoUtil: JobInfoUtil.setJobInfo():56 The max execution delay will be ignored for periodic task: daily_ping_task
07-05 14:42:09.710  1371 10359 I JobSchedulerImpl: JobSchedulerImpl.schedule():91 Schedule task: daily_ping_task (id=-1305128564). Success.
07-05 14:42:09.711  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.714 10254 10254 D InsetsController: show(ime(), fromIme=true)
07-05 14:42:09.719  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.757 10254 10254 D InsetsController: show(ime(), fromIme=true)
07-05 14:42:09.762 10254 10254 D InsetsController: show(ime(), fromIme=true)
07-05 14:42:09.781  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.781  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.790  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.800  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.800  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.808   796   966 D EGL_emulation: app_time_stats: avg=1185.15ms min=1185.15ms max=1185.15ms count=1
07-05 14:42:09.808  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.817  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.818  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.821  1371  1371 I KeyboardViewHelper: KeyboardViewHelper.getView():170 Get view with height ratio:1.000000
07-05 14:42:09.827  1274  1290 I FontLog : Received query Google Sans Text, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:42:09.827  1371  1804 I SP      : GC for 'kc_tflite_model' (10) with ttl of 0 ms took 0 ms (0/0/0)
07-05 14:42:09.828  1274  1290 I FontLog : Query [Google Sans Text] resolved to {Google Sans Text, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:09.828  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.830  1274  1290 I FontLog : Font PFD returned from cache for {Google Sans Text, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:09.830  1274  1290 I FontLog : Fetch {Google Sans Text, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:42:09.835  1371  1804 I SP      : GC for 'kc_tflite_model' (10) with ttl of 0 ms took 0 ms (0/0/0)
07-05 14:42:09.835  1274  1290 I FontLog : Pulling font file for id = 39, cache size = 6 [CONTEXT service_id=132 ]
07-05 14:42:09.838  1371  1804 I AbstractSyncResultCallback: AbstractSyncResultCallback.onFailure():36 onFailure(): kc_tflite_model.sync cancelled; expected if new request supersedes pending one.
07-05 14:42:09.838  1371  1804 I SP      : GC for 'delight' (10) with ttl of 0 ms took 0 ms (0/0/0)
07-05 14:42:09.839  1371  1804 I AbstractSyncResultCallback: AbstractSyncResultCallback.onFailure():36 onFailure(): kc_tflite_model.sync cancelled; expected if new request supersedes pending one.
07-05 14:42:09.840  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.841  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.842  1371 10343 I AbstractSyncResultCallback: AbstractSyncResultCallback.onFailure():36 onFailure(): delight.sync cancelled; expected if new request supersedes pending one.
07-05 14:42:09.842  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:42:09.843  1371  1804 I SuperDelight: SuperDelightBundledSlicingStrategy.getSlices():39 BundledSlicing#getSlices() : Locale = [en_US]
07-05 14:42:09.844  1371  1804 I SuperDelight: SuperDelightBundledSlicingStrategy.getSlices():65 BundledSlicing#getSlices(): result {slices=[bundled_delight:main_en_us_2022051700_2], last batch=true, sync metadata=false}
07-05 14:42:09.845  1371  1804 I SP      : Syncing bundled_delight (2022101700) with slices: [main_en_us_2022051700_2], metadata: false
07-05 14:42:09.846  1371  1804 I AbstractSyncResultCallback: AbstractSyncResultCallback.onFailure():36 onFailure(): delight.sync cancelled; expected if new request supersedes pending one.
07-05 14:42:09.847  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.848  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = HEADER, shownType = SHOW_OPTIONAL
07-05 14:42:09.848  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:42:09.849  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = BODY, shownType = SHOW_MANDATORY
07-05 14:42:09.849  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = true
07-05 14:42:09.850  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3483 setKeyboardViewShown() : type = FLOATING_CANDIDATES, shownType = HIDE
07-05 14:42:09.850  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.setKeyboardViewShown():3530 setKeyboardViewShown() : shouldShow = false
07-05 14:42:09.851  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.common.BIND_SHARED_PREFS pkg=com.google.android.gms }
07-05 14:42:09.852  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.853  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.856  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.857  1371  1371 I KeyboardViewUtil: KeyboardViewUtil.calculateMaxKeyboardBodyHeight():40 leave 354 height for app when screen height:2274, header height:116 and isFullscreenMode:false, so the max keyboard body height is:1804
07-05 14:42:09.859  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.867  1371  1371 I Choreographer: Skipped 36 frames!  The application may be doing too much work on its main thread.
07-05 14:42:09.882  1371  1371 I AndroidIME: KeyboardViewManager.updateKeyboardBodyViewHolderPaddingBottom():604 Set finalPaddingBottom = 0 while keyboardBottomGapFromScreen = 0; navigationHeight = 126
07-05 14:42:09.883  1371  1371 I AndroidIME: KeyboardViewManager.saveKeyboardBottomGap():282 realScreenHeight: 0 screenHeightInInches: 5.714286
07-05 14:42:09.883  1371  1371 I AndroidIME: keyboardHolderHeight: 757 navigationHeight: 126
07-05 14:42:09.883  1371  1371 I AndroidIME: getKeyboardBodyViewHolderPaddingBottom(): 0 getKeyboardBottomGapFromScreen(): 0
07-05 14:42:09.883  1371  1371 I AndroidIME: keyboardBottomGap: 126 bodyViewHolderBottomPadding: 0
07-05 14:42:09.883  1371  1371 I AndroidIME: decorViewStableInsetBottom: 126 updated: false
07-05 14:42:09.893  1371  1371 I LstmExtension: LstmExtension.onCreateServiceInternal():120 onCreateServiceInternal()
07-05 14:42:09.894  1371  1371 I LstmExtension: LstmExtension.onCreateServiceInternal():127 onCreateServiceInternal(): Finished in 0 ms
07-05 14:42:09.894  1274  1290 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:42:09.895  1274  1290 I FontLog : Query [Noto Color Emoji Compat] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:42:09.898  1274  1290 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:42:09.906  1274  1290 I FontLog : Pulling font file for id = 40, cache size = 5 [CONTEXT service_id=132 ]
07-05 14:42:09.910  1274  1290 I FontLog : Pulling font file for id = 40, cache size = 5 [CONTEXT service_id=132 ]
07-05 14:42:09.934  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.936  1371  1371 I DeviceModeUtil: DeviceModeUtil.getDeviceMode():122 Get device mode phone by ui mode:1 and smallestScreenWidthDp:411
07-05 14:42:09.939  1371  1371 I NormalModeController: NormalModeController.getKeyboardBodyViewHolderPaddingBottom():116 currentPrimeKeyboardType:SOFT systemPaddingBottom:-1
07-05 14:42:09.959  1371  1804 I SuperDelight: SuperDelightDownloadMetadataParser.parse():185 SuperDelightDownloadMetadataParser#parse(delight): Manifest parsed with 885 packs
07-05 14:42:09.960  1371 10345 I SuperDelight: SuperDelightManager$2.onSuccess():635 delight sync success, adding to data file manager
07-05 14:42:09.961  1371 10345 I DataFileManager: DataFileManager.addDataWithoutNotify():162 data en-840 already exists
07-05 14:42:09.962  1371 10345 I DataFileManager: DataFileManager.addDataWithoutNotify():162 data en-US already exists
07-05 14:42:09.963  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 3<6, inputStateId=2, lastInputStateId=4
07-05 14:42:09.967 10254 10254 I ViewInteraction: Performing 'close keyboard' action on view view.getId() is <**********/com.kewtoms.whatappsdo:id/search_field>
07-05 14:42:09.970 10254 10254 D IdlingRegistry: Registering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@c44e2ea]
07-05 14:42:10.013  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInputView():2241
07-05 14:42:10.015  1371  1371 W TooltipLifecycleManager: TooltipLifecycleManager.dismissTooltips():159 Tooltip with id spell_check_add_to_dictionary not found in tooltipManager.
07-05 14:42:10.022  1371  1371 I InputBundle: InputBundle.consumeEvent():923 Skip consuming an event as keyboard status is 0
07-05 14:42:10.023  1371  1371 I KeyboardWrapper: KeyboardWrapper.consumeEvent():275 Skip consuming an event as current keyboard is deactivated (state=0, keyboard existence=true)
07-05 14:42:10.024  1371  1371 I VoiceInputManagerWrapper: VoiceInputManagerWrapper.shutdown():77 shutdown()
07-05 14:42:10.029  1371  1371 I AndroidIME: AbstractIme.onDeactivate():207 LatinIme.onDeactivate()
07-05 14:42:10.044  1371  1872 E native  : E0000 00:00:1751726530.043892    1872 keyboard.cc:27] Cannot create a keyboard with 0 valid keys
07-05 14:42:10.061  1371  1804 I SP      : Sync for emoji succeeded in 636 ms: {added: [60932fe37d5f6d7a3e60488d8a1755c4], metadata: false
07-05 14:42:10.089  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_us_2022101700_1[2]
07-05 14:42:10.089  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_840_2022102600_1[2]
07-05 14:42:10.093  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: 2023012316_en[7]
07-05 14:42:10.094  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: 2023012316_en[7]
07-05 14:42:10.094  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: 2023012316_en[7]
07-05 14:42:10.095  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: 2023012316_en[7]
07-05 14:42:10.095  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: 2023012316_en[7]
07-05 14:42:10.095  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: 2023012316_en[7]
07-05 14:42:10.095  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: tflite-nwp-be541314788c7304827a9710c3ac4edb[3]
07-05 14:42:10.107  1371  1804 I SP      : GC for 'bundled_delight' (10) with ttl of 0 ms took 0 ms (0/0/0)
07-05 14:42:10.109  1371  1804 I LmManager: LmManager$1.onFailure():81 Bundled delight sync failed
07-05 14:42:10.109  1371  1804 I LmManager: java.util.concurrent.CancellationException: Task was cancelled.
07-05 14:42:10.109  1371  1804 I LmManager: 	at mrp.s(PG:3)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mrp.get(PG:4)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mgr.Q(PG:1)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mfu.V(PG:2)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mtg.run(PG:4)
07-05 14:42:10.109  1371  1804 I LmManager: 	at msq.execute(PG:1)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mrp.q(PG:1)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mrp.k(PG:12)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mrp.p(PG:5)
07-05 14:42:10.109  1371  1804 I LmManager: 	at msm.c(PG:2)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mso.e(PG:2)
07-05 14:42:10.109  1371  1804 I LmManager: 	at mtr.run(PG:8)
07-05 14:42:10.109  1371  1804 I LmManager: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:463)
07-05 14:42:10.109  1371  1804 I LmManager: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 14:42:10.109  1371  1804 I LmManager: 	at hbs.run(PG:2)
07-05 14:42:10.109  1371  1804 I LmManager: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
07-05 14:42:10.109  1371  1804 I LmManager: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
07-05 14:42:10.109  1371  1804 I LmManager: 	at java.lang.Thread.run(Thread.java:1012)
07-05 14:42:10.109  1371  1804 I LmManager: 	at hbf.run(PG:2)
07-05 14:42:10.109  1371  1804 I SP      : Sync for bundled_emoji succeeded in 683 ms: no changes
07-05 14:42:10.110  1371  1804 I SuperDelight: SuperDelightMergingStrategy.merge():55 SuperDelightMergingStrategy#merge(): selected[[main_en_840_2022102600_1, main_en_us_2022101700_1]] synced[[main_en_840_2022102600_1]]
07-05 14:42:10.111  1371  1804 I SP      : Sync for kc_tflite_model succeeded in 683 ms: no changes
07-05 14:42:10.111  1371  1804 I SP      : GC for 'emoji' (10) with ttl of 0 ms took 0 ms (0/0/0)
07-05 14:42:10.112  1371  1804 I AbstractSyncResultCallback: AbstractSyncResultCallback.onFailure():36 onFailure(): bundled_delight.sync cancelled; expected if new request supersedes pending one.
07-05 14:42:10.113  1371  1804 I SP      : GC for 'bundled_emoji' (10) with ttl of 0 ms took 0 ms (0/0/0)
07-05 14:42:10.114  1371  1804 I SP      : Sync for delight succeeded in 645 ms: no changes
07-05 14:42:10.115  1371  1804 I SP      : GC for 'kc_tflite_model' (10) with ttl of 0 ms took 1 ms (0/1/0)
07-05 14:42:10.118  1371 10342 I SuperDelight: SuperDelightManager$2.onSuccess():635 bundled_delight sync success, adding to data file manager
07-05 14:42:10.118  1371 10342 I DataFileManager: DataFileManager.removeDataWithoutNotify():199 removing data en-US bundled_delight
07-05 14:42:10.119  1371  1804 I SP      : GC for 'delight' (10) with ttl of 0 ms took 0 ms (0/0/0)
07-05 14:42:10.121  1371 10358 I EmojiSuperpacksManager: EmojiSuperpacksManager.processPackSet():498 processPackSet() : isBundled = false
07-05 14:42:10.121  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.getEmojiShortcuts():117 Reading en_US emoji shortcuts
07-05 14:42:10.150  1371 10358 I EmojiSuperpacksManager: EmojiSuperpacksManager.processPackSet():498 processPackSet() : isBundled = true
07-05 14:42:10.150  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.getEmojiShortcuts():134 Read en_US emoji shortcuts successfully.
07-05 14:42:10.154  1371 10345 I SuperDelight: SuperDelightManager$2.onSuccess():635 delight sync success, adding to data file manager
07-05 14:42:10.154  1371  1804 I SP      : Sync for bundled_delight succeeded in 316 ms: {added: [main_en_us_2022051700_2], metadata: false
07-05 14:42:10.155  1371 10345 I DataFileManager: DataFileManager.addDataWithoutNotify():162 data en-840 already exists
07-05 14:42:10.155  1371 10345 I DataFileManager: DataFileManager.addDataWithoutNotify():162 data en-US already exists
07-05 14:42:10.166  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.call():63 1 emoji shortcut maps loaded.
07-05 14:42:10.171  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.call():72 Finished loading emoji shortcuts
07-05 14:42:10.172  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.getEmojiShortcuts():117 Reading en_US emoji shortcuts
07-05 14:42:10.187  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.getEmojiShortcuts():134 Read en_US emoji shortcuts successfully.
07-05 14:42:10.190  1371  1804 I SP      : GC for 'bundled_delight' (10) with ttl of 0 ms took 1 ms (0/0/1)
07-05 14:42:10.190  1371  1804 I LmManager: LmManager$1.onSuccess():76 Bundled delight sync success
07-05 14:42:10.193  1371 10342 I SuperDelight: SuperDelightManager$2.onSuccess():635 bundled_delight sync success, adding to data file manager
07-05 14:42:10.194  1371 10342 I DataFileManager: DataFileManager.addDataWithoutNotify():166 adding data en-US bundled_delight
07-05 14:42:10.197  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.call():63 1 emoji shortcut maps loaded.
07-05 14:42:10.201  1371  1805 I EmojiShortcutsLoader: EmojiShortcutsLoader.call():72 Finished loading emoji shortcuts
07-05 14:42:10.208  1371  2057 I native  : I0000 00:00:1751726530.208588    2057 input-context-store.cc:255] Ignoring stale client request for FetchSuggestions
07-05 14:42:10.229  1371  2057 I native  : I0000 00:00:1751726530.229415    2057 input-context-store.cc:178] Ignoring stale client request for OverrideDecodedCandidates
07-05 14:42:10.230  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 14:42:10.230  1371  1371 W InputContextProxyV4: InputContextProxyV4.applyClientDiffInternal():897 Ignore [FetchSuggestions] diff due to stale request: 5<11, inputStateId=0, lastInputStateId=7
07-05 14:42:10.239  1371  2057 I Delight5Decoder: Delight5DecoderWrapper.setKeyboardLayout():457 setKeyboardLayout()
07-05 14:42:10.243  1371  2057 W Delight5Decoder: Delight5DecoderWrapper.shouldAbandonMessage():1983 Detected old [FetchSuggestions] request in background: 10<11
07-05 14:42:10.258   398   429 W TransactionTracing: Could not find layer handle 0x71102f7cfd90
07-05 14:42:10.374 10254 10254 D IdlingRegistry: Unregistering idling resources: [androidx.test.espresso.action.CloseKeyboardAction$CloseKeyboardIdlingResult@c44e2ea]
07-05 14:42:10.377 10254 10254 I ViewInteraction: Performing 'single click' action on view view.getId() is <2131230836/com.kewtoms.whatappsdo:id/button_search>
07-05 14:42:10.380  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=6921378, downTime=6921378, deviceId=-1, source=0x1002, displayId=0, eventId=-126564433 }
07-05 14:42:10.422  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=540.0, y[0]=1099.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=6921419, downTime=6921378, deviceId=-1, source=0x1002, displayId=0, eventId=-962742412 }
07-05 14:42:10.426 10254 10369 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 14:42:10.443 10254 10254 I APP:HomeFragment: onCreateView: searchButton onClick
07-05 14:42:10.445 10254 10254 I APP:HomeFragment: onCreateView: userUsageCount: 0
07-05 14:42:10.452   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(34)
07-05 14:42:10.506 10254 10254 I APP:AppSearcher: Searching for app: eat
07-05 14:42:10.507 10254 10254 I APP:AppSearcher: jsonBody:{"data_str":"{\"allowSearch\":true,\"appIds\":[\"com.android.camera2\",\"com.android.chrome\",\"com.android.settings\",\"com.google.android.apps.docs\",\"com.google.android.apps.maps\",\"com.google.android.apps.messaging\",\"com.google.android.apps.photos\",\"com.google.android.apps.youtube.music\",\"com.google.android.calendar\",\"com.google.android.contacts\",\"com.google.android.deskclock\",\"com.google.android.dialer\",\"com.google.android.gm\",\"com.google.android.youtube\",\"com.android.stk\",\"com.google.android.documentsui\",\"com.google.android.googlequicksearchbox\"],\"query\":\"eat\"}"}
07-05 14:42:10.507 10254 10254 I APP:AppSearcher: searchApp: Sending request to:http://localhost:8000/__mock_search_user_app
07-05 14:42:10.509 10254 10254 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:42:10.510 10254 10254 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:42:10.510 10254 10373 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:42:10.511 10254 10373 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:42:10.514 10254 10373 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:42:10.515 10254 10254 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:42:10.517 10254 10375 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:42:10.518 10254 10375 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:42:10.518 10254 10254 I APP:HomeFragment: Hided soft keyboard
07-05 14:42:10.521 10254 10375 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:42:10.558 10254 10375 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:42:10.565   336 10379 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:10.567   336 10380 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10196 0}
07-05 14:42:10.571 10254 10254 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:42:10.572 10254 10254 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:42:10.572 10254 10254 I APP:AppSearcher: onPostSuccess: responseJsonObj:{"code":200,"is_success":true,"message":"Operation successful.","data":{"packageNames":"[com.whatsapp, com.facebook.katana, com.instagram.android]","scores":"[0.95, 0.87, 0.82]"}}
07-05 14:42:13.688  2019  2030 W earchbox:search: Reducing the number of considered missed Gc histogram windows from 224 to 100
07-05 14:42:15.008  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: tflite-nwp-be541314788c7304827a9710c3ac4edb[3]
07-05 14:42:15.008  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_840_2022102600_1[4]
07-05 14:42:15.008  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_us_2022101700_1[4]
07-05 14:42:15.009  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_840_2022102600_1[4]
07-05 14:42:15.009  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_us_2022101700_1[4]
07-05 14:42:15.009  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_840_2022102600_1[4]
07-05 14:42:15.009  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_us_2022101700_1[4]
07-05 14:42:15.009  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_us_2022051700_2[2]
07-05 14:42:15.009  1371  1383 W SP      : File ref is being finalized but wasn't closed, file: main_en_us_2022051700_2[2]
07-05 14:42:16.026 10254 10254 D CompatibilityChangeReporter: Compat change id reported: 147798919; UID 10196; state: ENABLED
07-05 14:42:16.047 10254 10308 D EGL_emulation: app_time_stats: avg=530.21ms min=13.16ms max=5470.06ms count=12
07-05 14:42:16.047   796   966 W Parcel  : Expecting binder but got null!
07-05 14:42:16.074 10254 10263 W toms.whatappsdo: Cleared Reference was only reachable from finalizer (only reported once)
07-05 14:42:16.079   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:42:16.106 10254 10265 W System  : A resource failed to call close.
07-05 14:42:16.106 10254 10265 W System  : A resource failed to call close.
07-05 14:42:16.106 10254 10265 W System  : A resource failed to call close.
07-05 14:42:16.865  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.feedback.internal.IFeedbackService dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 14:42:16.934  1274  1274 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.icing.LIGHTWEIGHT_INDEX_SERVICE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 14:42:18.821 10254 10278 I TestRunner: finished: testSearchHasReturn(com.kewtoms.whatappsdo.model.AppSearcherTest)
