<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\my_projects\WhatAppsDo\app\src\main\assets"><file name="code_run_for_test/testSearchHasReturn.properties" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\code_run_for_test\testSearchHasReturn.properties"/><file name="code_run_for_test/test_sign_in_success_mock.properties" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\code_run_for_test\test_sign_in_success_mock.properties"/><file name="code_run_for_test/test_silent_sign_in_refresh_token_expired.properties" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\code_run_for_test\test_silent_sign_in_refresh_token_expired.properties"/><file name="code_run_for_test/test_silent_sign_in_success_mock.properties" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\code_run_for_test\test_silent_sign_in_success_mock.properties"/><file name="enum.txt" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\enum.txt"/><file name="python_constants/api_constant_keys.py" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\python_constants\api_constant_keys.py"/><file name="python_constants/user_database_constants.py" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\python_constants\user_database_constants.py"/><file name="code_run_for_test/test_sign_in_failure_mock.properties" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\code_run_for_test\test_sign_in_failure_mock.properties"/><file name="python_constants/api_response_utils_constants.py" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\python_constants\api_response_utils_constants.py"/><file name="code_run_for_test/testSearchHasNoReturn.properties" path="D:\code\my_projects\WhatAppsDo\app\src\main\assets\code_run_for_test\testSearchHasNoReturn.properties"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\my_projects\WhatAppsDo\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\my_projects\WhatAppsDo\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>