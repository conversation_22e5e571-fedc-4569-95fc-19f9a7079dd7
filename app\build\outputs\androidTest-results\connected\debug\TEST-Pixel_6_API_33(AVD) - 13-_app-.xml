<?xml version='1.0' encoding='UTF-8' ?>
<testsuite name="com.kewtoms.whatappsdo.model.AppSearcherTest" tests="1" failures="1" errors="0" skipped="0" time="0.807" timestamp="2025-07-05T14:55:53" hostname="localhost">
  <properties>
    <property name="device" value="Pixel_6_API_33(AVD) - 13" />
    <property name="flavor" value="" />
    <property name="project" value=":app" />
  </properties>
  <testcase name="testSearchHasNoReturn" classname="com.kewtoms.whatappsdo.model.AppSearcherTest" time="0.04">
    <failure>java.lang.RuntimeException: Failed to load config: code_run_for_test/testSearchHasNoReturn.properties
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:61)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:180)
... 33 trimmed
Caused by: java.io.FileNotFoundException: code_run_for_test/testSearchHasNoReturn.properties
at android.content.res.AssetManager.nativeOpenAsset(Native Method)
at android.content.res.AssetManager.open(AssetManager.java:904)
at android.content.res.AssetManager.open(AssetManager.java:881)
at com.kewtoms.whatappsdo.utils.CodeRunManager.loadConfig(CodeRunManager.java:58)
... 35 more
</failure>
  </testcase>
</testsuite>