<?xml version='1.0' encoding='UTF-8' ?>
<testsuite name="com.kewtoms.whatappsdo.model.AppSearcherTest" tests="1" failures="1" errors="0" skipped="0" time="10.393" timestamp="2025-07-05T14:58:14" hostname="localhost">
  <properties>
    <property name="device" value="Pixel_6_API_33(AVD) - 13" />
    <property name="flavor" value="" />
    <property name="project" value=":app" />
  </properties>
  <testcase name="testSearchHasNoReturn" classname="com.kewtoms.whatappsdo.model.AppSearcherTest" time="9.112">
    <failure>androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;
Got: was &lt;androidx.recyclerview.widget.RecyclerView{c06cd81 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}&gt;
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams&#64;YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at dalvik.system.VMStack.getThreadStackTrace(Native Method)
at java.lang.Thread.getStackTrace(Thread.java:1841)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:262)
... 33 trimmed
Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least &lt;1&gt;
Got: was &lt;androidx.recyclerview.widget.RecyclerView{c06cd81 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}&gt;
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams&#64;YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
</failure>
  </testcase>
</testsuite>