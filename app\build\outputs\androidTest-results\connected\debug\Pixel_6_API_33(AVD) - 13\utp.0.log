信息: Constructing runner from config.
信息: Configuring Android Instrumentation driver: android_instrumentation_runtime {
  instrumentation_info {
    app_package: "com.kewtoms.whatappsdo"
    test_package: "com.kewtoms.whatappsdo.test"
    test_runner_class: "androidx.test.runner.AndroidJUnitRunner"
  }
  instrumentation_args {
    args_map {
      key: "additionalTestOutputDir"
      value: "/sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output"
    }
    args_map {
      key: "class"
      value: "com.kewtoms.whatappsdo.model.AppSearcherTest#testSearchHasNoReturn"
    }
    args_map {
      key: "debug"
      value: "true"
    }
  }
}
am_instrument_timeout: 31536000

信息: Configuring AndroidTestApkInstallerPlugin: apks_to_install {
  apk_paths: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\intermediates\\apk\\debug\\app-debug.apk"
  install_options {
  }
  uninstall_after_test: true
}
apks_to_install {
  apk_paths: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\intermediates\\apk\\androidTest\\debug\\app-debug-androidTest.apk"
  install_options {
  }
  uninstall_after_test: true
}

信息: No installables found in test fixture. Nothing to install.
信息: Installing [D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\debug\app-debug.apk] on device emulator-5554.
信息: Installing [D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\androidTest\debug\app-debug-androidTest.apk] on device emulator-5554.
信息: Start logcat streaming.
信息: Running Android Instrumentation driver.
信息: Copying files from device to host: /sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output to D:\code\my_projects\WhatAppsDo\app\build\outputs\connected_android_test_additional_output\debugAndroidTest\connected\Pixel_6_API_33(AVD) - 13
信息: Copying /sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output/view-op-error-1.png to D:\code\my_projects\WhatAppsDo\app\build\outputs\connected_android_test_additional_output\debugAndroidTest\connected\Pixel_6_API_33(AVD) - 13\view-op-error-1.png
信息: Stop logcat streaming.
信息: Uninstalling com.kewtoms.whatappsdo for device emulator-5554.
信息: Uninstalling com.kewtoms.whatappsdo.test for device emulator-5554.
严重: Execute com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn: FAILED
junit.framework.AssertionFailedError: androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at dalvik.system.VMStack.getThreadStackTrace(Native Method)
at java.lang.Thread.getStackTrace(Thread.java:1841)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:271)
... 33 trimmed
Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

androidx.test.espresso.base.AssertionErrorHandler$AssertionFailedWithCauseError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at dalvik.system.VMStack.getThreadStackTrace(Native Method)
at java.lang.Thread.getStackTrace(Thread.java:1841)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:3)
at androidx.test.espresso.base.AssertionErrorHandler.handleSafely(AssertionErrorHandler.java:1)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:5)
at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
at com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasNoReturn(AppSearcherTest.java:271)
... 33 trimmed
Caused by: junit.framework.AssertionFailedError: 'not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>' doesn't match the selected view.
Expected: not an instance of android.view.ViewGroup and viewGroup.getChildCount() to be at least <1>
Got: was <androidx.recyclerview.widget.RecyclerView{7e7fa67 VFED..... ........ 0,1704-1080,1878 #7f0800fb app:id/id_app_icon_text_recycler}>
View Details: RecyclerView{id=2131230971, res-name=id_app_icon_text_recycler, visibility=VISIBLE, width=1080, height=174, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1704.0, child-count=6}

at androidx.test.espresso.matcher.ViewMatchers.assertThat(ViewMatchers.java:16)
at androidx.test.espresso.assertion.ViewAssertions$MatchesViewAssertion.check(ViewAssertions.java:7)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAssertion.check(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:14)
at androidx.test.espresso.ViewInteraction$2.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
