信息: Constructing runner from config.
信息: Configuring Android Instrumentation driver: android_instrumentation_runtime {
  instrumentation_info {
    app_package: "com.kewtoms.whatappsdo"
    test_package: "com.kewtoms.whatappsdo.test"
    test_runner_class: "androidx.test.runner.AndroidJUnitRunner"
  }
  instrumentation_args {
    args_map {
      key: "additionalTestOutputDir"
      value: "/sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output"
    }
    args_map {
      key: "class"
      value: "com.kewtoms.whatappsdo.model.AppSearcherTest#testSearchHasReturn"
    }
    args_map {
      key: "debug"
      value: "true"
    }
  }
}
am_instrument_timeout: 31536000

信息: Configuring AndroidTestApkInstallerPlugin: apks_to_install {
  apk_paths: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\intermediates\\apk\\debug\\app-debug.apk"
  install_options {
  }
  uninstall_after_test: true
}
apks_to_install {
  apk_paths: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\intermediates\\apk\\androidTest\\debug\\app-debug-androidTest.apk"
  install_options {
  }
  uninstall_after_test: true
}

信息: No installables found in test fixture. Nothing to install.
信息: Installing [D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\debug\app-debug.apk] on device emulator-5554.
信息: Installing [D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\androidTest\debug\app-debug-androidTest.apk] on device emulator-5554.
信息: Start logcat streaming.
信息: Running Android Instrumentation driver.
信息: Copying files from device to host: /sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output to D:\code\my_projects\WhatAppsDo\app\build\outputs\connected_android_test_additional_output\debugAndroidTest\connected\Pixel_6_API_33(AVD) - 13
信息: Stop logcat streaming.
信息: Uninstalling com.kewtoms.whatappsdo for device emulator-5554.
信息: Uninstalling com.kewtoms.whatappsdo.test for device emulator-5554.
信息: Execute com.kewtoms.whatappsdo.model.AppSearcherTest.testSearchHasReturn: PASSED
