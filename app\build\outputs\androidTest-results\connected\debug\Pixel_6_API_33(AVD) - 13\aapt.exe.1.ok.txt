EXECUTING: C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\aapt.exe dump badging D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\debug\app-debug.apk
CURRENT_WORKING_DIRECTORY: D:\code\my_projects\WhatAppsDo
START_TIME: 2025-07-05 23:25:49.143
START_TIME-NANOS: 2025-07-05 23:25:49.143262300
ENVIRONMENT:

*****************************************
STDOUT/STDERR BELOW
===================
package: name='com.kewtoms.whatappsdo' versionCode='408' versionName='1.108.15' platformBuildVersionName='14' platformBuildVersionCode='34' compileSdkVersion='34' compileSdkVersionCodename='14'
sdkVersion:'26'
targetSdkVersion:'34'
uses-permission: name='android.permission.QUERY_ALL_PACKAGES'
uses-permission: name='android.permission.INTERNET'
uses-permission: name='android.permission.ACCESS_NETWORK_STATE'
uses-permission: name='com.google.android.providers.gsf.permission.READ_GSERVICES'
uses-permission: name='com.kewtoms.whatappsdo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION'
uses-permission: name='android.permission.REORDER_TASKS'
application-label:'WhatAppsDo'
application-label-af:'WhatAppsDo'
application-label-am:'WhatAppsDo'
application-label-ar:'WhatAppsDo'
application-label-as:'WhatAppsDo'
application-label-az:'WhatAppsDo'
application-label-be:'WhatAppsDo'
application-label-bg:'WhatAppsDo'
application-label-bn:'WhatAppsDo'
application-label-bs:'WhatAppsDo'
application-label-ca:'WhatAppsDo'
application-label-cs:'WhatAppsDo'
application-label-da:'WhatAppsDo'
application-label-de:'WhatAppsDo'
application-label-el:'WhatAppsDo'
application-label-en-AU:'WhatAppsDo'
application-label-en-CA:'WhatAppsDo'
application-label-en-GB:'WhatAppsDo'
application-label-en-IN:'WhatAppsDo'
application-label-en-XC:'WhatAppsDo'
application-label-es:'WhatAppsDo'
application-label-es-419:'WhatAppsDo'
application-label-es-US:'WhatAppsDo'
application-label-et:'WhatAppsDo'
application-label-eu:'WhatAppsDo'
application-label-fa:'WhatAppsDo'
application-label-fi:'WhatAppsDo'
application-label-fr:'WhatAppsDo'
application-label-fr-CA:'WhatAppsDo'
application-label-gl:'WhatAppsDo'
application-label-gu:'WhatAppsDo'
application-label-hi:'WhatAppsDo'
application-label-hr:'WhatAppsDo'
application-label-hu:'WhatAppsDo'
application-label-hy:'WhatAppsDo'
application-label-in:'WhatAppsDo'
application-label-is:'WhatAppsDo'
application-label-it:'WhatAppsDo'
application-label-iw:'WhatAppsDo'
application-label-ja:'WhatAppsDo'
application-label-ka:'WhatAppsDo'
application-label-kk:'WhatAppsDo'
application-label-km:'WhatAppsDo'
application-label-kn:'WhatAppsDo'
application-label-ko:'WhatAppsDo'
application-label-ky:'WhatAppsDo'
application-label-lo:'WhatAppsDo'
application-label-lt:'WhatAppsDo'
application-label-lv:'WhatAppsDo'
application-label-mk:'WhatAppsDo'
application-label-ml:'WhatAppsDo'
application-label-mn:'WhatAppsDo'
application-label-mr:'WhatAppsDo'
application-label-ms:'WhatAppsDo'
application-label-my:'WhatAppsDo'
application-label-nb:'WhatAppsDo'
application-label-ne:'WhatAppsDo'
application-label-nl:'WhatAppsDo'
application-label-or:'WhatAppsDo'
application-label-pa:'WhatAppsDo'
application-label-pl:'WhatAppsDo'
application-label-pt:'WhatAppsDo'
application-label-pt-BR:'WhatAppsDo'
application-label-pt-PT:'WhatAppsDo'
application-label-ro:'WhatAppsDo'
application-label-ru:'WhatAppsDo'
application-label-si:'WhatAppsDo'
application-label-sk:'WhatAppsDo'
application-label-sl:'WhatAppsDo'
application-label-sq:'WhatAppsDo'
application-label-sr:'WhatAppsDo'
application-label-sr-Latn:'WhatAppsDo'
application-label-sv:'WhatAppsDo'
application-label-sw:'WhatAppsDo'
application-label-ta:'WhatAppsDo'
application-label-te:'WhatAppsDo'
application-label-th:'WhatAppsDo'
application-label-tl:'WhatAppsDo'
application-label-tr:'WhatAppsDo'
application-label-uk:'WhatAppsDo'
application-label-ur:'WhatAppsDo'
application-label-uz:'WhatAppsDo'
application-label-vi:'WhatAppsDo'
application-label-zh-CN:'WhatAppsDo'
application-label-zh-HK:'WhatAppsDo'
application-label-zh-TW:'WhatAppsDo'
application-label-zu:'WhatAppsDo'
application-icon-120:'res/mipmap-anydpi-v26/ic_launcher.xml'
application-icon-160:'res/mipmap-anydpi-v26/ic_launcher.xml'
application-icon-240:'res/mipmap-anydpi-v26/ic_launcher.xml'
application-icon-320:'res/mipmap-anydpi-v26/ic_launcher.xml'
application-icon-480:'res/mipmap-anydpi-v26/ic_launcher.xml'
application-icon-640:'res/mipmap-anydpi-v26/ic_launcher.xml'
application-icon-65534:'res/mipmap-anydpi-v26/ic_launcher.xml'
application-icon-65535:'res/mipmap-anydpi-v26/ic_launcher.xml'
application: label='WhatAppsDo' icon='res/mipmap-anydpi-v26/ic_launcher.xml'
testOnly='-1'
application-debuggable
launchable-activity: name='com.kewtoms.whatappsdo.MainActivity'  label='' icon=''
uses-library-not-required:'androidx.window.extensions'
uses-library-not-required:'androidx.window.sidecar'
feature-group: label=''
  uses-feature: name='android.hardware.faketouch'
  uses-implied-feature: name='android.hardware.faketouch' reason='default feature for all apps'
main
other-activities
other-receivers
other-services
supports-screens: 'small' 'normal' 'large' 'xlarge'
supports-any-density: 'true'
locales: '--_--' 'af' 'am' 'ar' 'as' 'az' 'be' 'bg' 'bn' 'bs' 'ca' 'cs' 'da' 'de' 'el' 'en-AU' 'en-CA' 'en-GB' 'en-IN' 'en-XC' 'es' 'es-419' 'es-US' 'et' 'eu' 'fa' 'fi' 'fr' 'fr-CA' 'gl' 'gu' 'hi' 'hr' 'hu' 'hy' 'in' 'is' 'it' 'iw' 'ja' 'ka' 'kk' 'km' 'kn' 'ko' 'ky' 'lo' 'lt' 'lv' 'mk' 'ml' 'mn' 'mr' 'ms' 'my' 'nb' 'ne' 'nl' 'or' 'pa' 'pl' 'pt' 'pt-BR' 'pt-PT' 'ro' 'ru' 'si' 'sk' 'sl' 'sq' 'sr' 'sr-Latn' 'sv' 'sw' 'ta' 'te' 'th' 'tl' 'tr' 'uk' 'ur' 'uz' 'vi' 'zh-CN' 'zh-HK' 'zh-TW' 'zu'
densities: '120' '160' '240' '320' '480' '640' '65534' '65535'
===================
END_TIME: 2025-07-05 23:25:49.214
END_TIME-NANOS: 2025-07-05 23:25:49.214616700
DURATION: 71ms
EXIT CODE: 0
